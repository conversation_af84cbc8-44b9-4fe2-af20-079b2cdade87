(function(e){function t(e){const o=document.getElementById(`path1_${e}`),i=document.getElementById(`path2_${e}`),a=document.getElementById(`path3_${e}`),n=document.getElementById(`path4_${e}`),w=document.getElementById(`rect-path1-1_${e}`),_=document.getElementById(`rect-path1-2_${e}`),y=document.getElementById(`rect-path2-1_${e}`),j=document.getElementById(`rect-path2-2_${e}`),b=document.getElementById(`rect-path3-1_${e}`),v=document.getElementById(`rect-path3-2_${e}`),u=document.getElementById(`rect-path4-1_${e}`),h=document.getElementById(`rect-path4-2_${e}`),m=document.getElementById(`rect-path4-3_${e}`);if(!o||!i||!a||!n)return;const f=o.getTotalLength(),p=i.getTotalLength(),g=a.getTotalLength(),d=n.getTotalLength(),l=8e3,c=9e3,r=6e3,s=4e3;function O(e,t,n,s){const o=t.getPointAtLength(s*n),i=t.getPointAtLength(Math.min(s*n+1,n)),a=Math.atan2(i.y-o.y,i.x-o.x)*180/Math.PI;e.setAttribute("x",o.x-20),e.setAttribute("y",o.y-1),e.setAttribute("transform",`rotate(${a} ${o.x} ${o.y})`)}function t(e,t,n,s,o=0){let i,a=0;function r(c){if(i||(i=c),a++,a%2===0){const a=(c-i+o)%s,r=a/s;O(e,t,n,r)}requestAnimationFrame(r)}requestAnimationFrame(r)}w&&_&&(t(w,o,f,l,0),t(_,o,f,l,l/2)),y&&j&&(t(y,i,p,c,0),t(j,i,p,c,c/2)),b&&v&&(t(b,a,g,r,0),t(v,a,g,r,r/2)),u&&h&&m&&(t(u,n,d,s,0),t(h,n,d,s,s/3),t(m,n,d,s,2*s/3))}function n(e){const s=[{path:`path1-${e}`,rect:`rect1-${e}`,speed:.005,startPos:.1,direction:1},{path:`path2-${e}`,rect:`rect2-${e}`,speed:.004,startPos:.9,direction:-1},{path:`path3-${e}`,rect:`rect3-${e}`,speed:.0035,startPos:.8,direction:-1},{path:`path4-${e}`,rect:`rect4-${e}`,speed:.003,startPos:.3,direction:-1},{path:`path5-${e}`,rect:`rect5-${e}`,speed:.0025,startPos:.5,direction:-1}],o=s.map(e=>{const t=document.getElementById(e.path),n=document.getElementById(e.rect);if(!t||!n)return null;const s=t.getTotalLength();return{path:t,rect:n,pathLength:s,progress:e.startPos||0,speed:e.speed||.003,direction:e.direction||1}}).filter(e=>e!==null);let t=0;function n(){t++,t%2===0&&o.forEach(e=>{e.progress+=e.speed*e.direction,e.progress>1?e.progress=0:e.progress<0&&(e.progress=1);const t=e.path.getPointAtLength(e.progress*e.pathLength),s=.001*e.direction,n=e.path.getPointAtLength((e.progress+s)*e.pathLength),o=Math.atan2(n.y-t.y,n.x-t.x)*180/Math.PI;e.rect.setAttribute("transform",`translate(${t.x}, ${t.y}) rotate(${o}) translate(-16, -1)`)}),requestAnimationFrame(n)}n()}function s(){window.innerWidth>=1200&&document.querySelectorAll(".pxl-icon-box__layout-2").forEach(e=>{const n=e.id.split("_")[1];t(n)}),document.querySelectorAll(".pxl-icon-box--layout-3").forEach(e=>{const t=e.id.split("_")[1];n(t)})}e(window).on("elementor/frontend/init",function(){elementorFrontend.hooks.addAction("frontend/element_ready/pxl_icon_box.default",function(){s()})})})(jQuery)