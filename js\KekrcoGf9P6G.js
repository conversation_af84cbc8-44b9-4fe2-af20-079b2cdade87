(function(e){"use strict";e(window).on("elementor/frontend/init",function(){const e=["pxl_post_carousel","pxl_testimonial_carousel","pxl_team_carousel","pxl_partner_carousel","pxl_language_carousel","pxl_image_carousel","pxl_icon_box_carousel","pxl_textbox_carousel"];e.forEach(e=>{elementorFrontend.hooks.addAction(`frontend/element_ready/${e}.default`,function(e){t(e)})})});function t(t){t.find(".pxl-swiper-slider").each(function(){var i,c,l,o=e(this),a=o.closest(t),n=o.find(".pxl-swiper-container").data().settings,s={direction:n.slide_direction,effect:n.slide_mode,wrapperClass:"pxl-swiper-slider__wrapper",slideClass:"pxl-swiper-slider__item",slidesPerView:n.slides_to_show,slidesPerGroup:n.slides_to_scroll,slidesPerColumn:n.slide_percolumn,spaceBetween:0,observer:!0,observeParents:!0,navigation:{nextEl:o.find(".pxl-swiper__nav .pxl-swiper__nav-next")[0],prevEl:o.find(".pxl-swiper__nav .pxl-swiper__nav-prev")[0]},pagination:{type:n.pagination_type,el:o.find(".pxl-swiper-dots")[0],clickable:!0,modifierClass:"pxl-swiper-pagination-",bulletClass:"swiper-pagination-bullet",renderCustom:(e,t,n,s)=>`${n} of ${s}`,renderBullet:(e,t)=>`<span class="${t}"></span>`},speed:n.speed,watchSlidesProgress:!0,watchSlidesVisibility:!0,breakpoints:{0:{slidesPerView:n.slides_to_show_xs,slidesPerGroup:n.slides_to_scroll},576:{slidesPerView:n.slides_to_show_sm,slidesPerGroup:n.slides_to_scroll},768:{slidesPerView:n.slides_to_show_md,slidesPerGroup:n.slides_to_scroll},992:{slidesPerView:n.slides_to_show_lg,slidesPerGroup:n.slides_to_scroll},1200:{slidesPerView:n.slides_to_show,slidesPerGroup:n.slides_to_scroll},1400:{slidesPerView:n.slides_to_show_xxl,slidesPerGroup:n.slides_to_scroll}},on:{init:function(e){r(e,a)},slideChange:function(e){r(e,a)},sliderMove:function(e){r(e,a)}}};if((n.center_slide===!0||n.center_slide==="true")&&(s.centeredSlides=!0),(n.loop===!0||n.loop==="true")&&(s.loop=!0),n.autoplay===!0||n.autoplay==="true"?s.autoplay={delay:parseInt(n.delay,10)||3e3,disableOnInteraction:n.pause_on_interaction===!0||n.pause_on_interaction==="true",reverseDirection:n.reverseDirection===!0||n.reverseDirection==="true",waitForTransition:!1}:s.autoplay=!1,n.creativeEffect==="card_rotate"&&(s.effect="creative",s.creativeEffect={perspective:!0,limitProgress:3,prev:{translate:["-100%","30px",-100],rotate:[0,0,-8],origin:"bottom"},next:{translate:["100%","30px",-100],rotate:[0,0,8],origin:"bottom"}}),n.creativeEffect==="card_3d"){let e;s.effect="coverflow",s.centeredSlides=!0,s.coverflowEffect={rotate:50,stretch:0,depth:60,modifier:1,slideShadows:!0},s.on={slideChange:function(){this.slides.forEach(e=>{window.innerWidth>1200&&(e.style.transition="transform 0.3s ease",e.style.transform=e.style.transform.replace(/scale\([^)]*\)/g,""),e.classList.contains("swiper-slide-active")?e.style.transform+=" scale(1.1)":e.style.transform+=" scale(0.9)")})},setTransition:function(e){this.slides.forEach(t=>{t.style.transitionDuration=`${e}ms`})}},s.on={setTranslate:function(){cancelAnimationFrame(e),e=requestAnimationFrame(()=>{this.slides.forEach(e=>{window.innerWidth>1200&&(e.style.transform=e.style.transform.replace(/scale\([^)]*\)/g,""),e.classList.contains("swiper-slide-active")?e.style.transform+=" scale(1.1)":e.style.transform+=" scale(0.9)")})})}}}o.find(".pxl-swiper-thumbs").length>0&&(i=o.find(".pxl-swiper-thumbs").data().settings,c=new Swiper(o.find(".pxl-swiper-thumbs")[0],{effect:i.slide_mode,direction:i.slide_direction,spaceBetween:0,slidesPerView:i.slides_to_show,centeredSlides:!1,loop:i.loop,watchSlidesProgress:!0,slideToClickedSlide:!0}),s.thumbs={swiper:c}),l=new Swiper(o.find(".pxl-swiper-container")[0],s),a.find(".testimonial-3 .pxl-swiper-slider__avatar").off("click.testimonial").on("click.testimonial",function(){var t=e(this).data("index");l.slideToLoop(t)}),n.autoplay==="true"&&n.pause_on_hover==="true"&&e(o.find(".pxl-swiper-container")).on({mouseenter:function(){this.swiper.autoplay.stop()},mouseleave:function(){this.swiper.autoplay.start()}});function r(e,t){var s=e.loopedSlides?e.realIndex:e.activeIndex,n=t.find(".pxl-swiper-slider__avatar"),i=n.length;n.removeClass("active-1 active-2 active active-4 active-5");function o(e){return(e+i)%i}n.eq(o(s)).addClass("active"),n.eq(o(s+1)).addClass("active-4"),n.eq(o(s-1)).addClass("active-2"),n.eq(o(s+2)).addClass("active-5"),n.eq(o(s-2)).addClass("active-1")}})}})(jQuery)