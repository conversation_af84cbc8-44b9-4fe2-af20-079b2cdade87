(function(e){e.fn.serializeArrayAll=function(){var t=/\r?\n/g;return this.map(function(){return this.elements?e.makeArray(this.elements):this}).map(function(n,s){var o=e(this).val();if(o==null)return o==null;if(this.type==="checkbox"){if(this.checked)return{name:this.name,value:this.checked?this.value:""}}else if(this.type==="radio"){if(this.checked)return{name:this.name,value:this.checked?this.value:""}}else return e.isArray(o)?e.map(o,function(e){return{name:s.name,value:e.replace(t,`\r
`)}}):{name:s.name,value:o.replace(t,`\r
`)}}).get()},e(document).on("wooaa_adding_to_cart",function(e,t){t.removeClass("added").addClass("loading")}),e(document).on("wooaa_added_to_cart",function(e,t,n,s){s.removeClass("loading").addClass("added")}),e(document).on("click",".single_add_to_cart_button:not("+wooaa_vars.ignore_btn_class+")",function(t){var o,i,r,c,l,a=!1,n=e(this),s=n.closest(".wpc_cart").length?n.closest(".wpc_cart"):n.closest("form.cart");if(wooaa_vars.product_types!==void 0&&(r=wooaa_vars.product_types.split(","),r.includes("all")?a=!0:r.forEach(function(e){n.is(".product-type-"+e+" .single_add_to_cart_button")&&(a=!0)})),a)return t.preventDefault(),o={},i=s.find('input:not([name="product_id"]), select, button, textarea').serializeArrayAll()||0,e.each(i,function(e,t){t.name==="add-to-cart"&&(t.name="product_id",t.value=s.find("input[name=variation_id]").val()||s.find("[name=variation_id]").val()||s.find("input.variation_id").val()||s.find(".variation_id").val()||s.find("input[name=add-to-cart]").val()||s.find("[name=add-to-cart]").val()||n.val())}),e(document.body).trigger("wooaa_adding_to_cart",[n,i]),e(document.body).trigger("adding_to_cart",[n,i]),e.each(i,function(e,t){t.name!==""&&(o[t.name]=t.value)}),n.is(".product-type-variable .single_add_to_cart_button")&&(c={},s.find('[name^="attribute_"]').each(function(){var t=e(this).attr("name");c[t]=e(this).val()}),o.variation=c),wooaa_vars.ignore_form_data!=void 0&&wooaa_vars.ignore_form_data!=""&&(l=wooaa_vars.ignore_form_data.split(","),l.forEach(function(e){delete o[e]})),o.action="wooaa_add_to_cart",o.nonce=wooaa_vars.nonce,e.post(wooaa_vars.wc_ajax_url.toString().replace("%%endpoint%%","wooaa_add_to_cart"),o,function(t){if(!t)return;if(t.error&&t.product_url){window.location=t.product_url;return}if(wooaa_vars.cart_redirect_after_add==="yes"){window.location=wooaa_vars.cart_url;return}e(document.body).trigger("added_to_cart",[t.fragments,t.cart_hash,n]),e(document.body).trigger("wooaa_added_to_cart",[t.fragments,t.cart_hash,n])}),!1})})(jQuery)