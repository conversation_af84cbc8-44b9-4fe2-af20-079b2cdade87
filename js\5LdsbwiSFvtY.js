(function(e){"use strict";function n(t){setTimeout(function(){var n=t.find(".pxl-split-text");if(n.length==0)return;gsap.registerPlugin(SplitText),n.each(function(t,n){n.split=new SplitText(n,{type:"lines,words,chars",linesClass:"split-line"}),gsap.set(n,{perspective:400}),e(n).hasClass("split-in-fade")&&(e(n).addClass("active"),gsap.set(n.split.chars,{opacity:0,ease:"Back.easeOut"})),e(n).hasClass("split-in-right")&&gsap.set(n.split.chars,{opacity:0,x:"50",ease:"Back.easeOut"}),e(n).hasClass("split-in-left")&&gsap.set(n.split.chars,{opacity:0,x:"-50",ease:"circ.out"}),e(n).hasClass("split-in-up")&&gsap.set(n.split.chars,{opacity:0,y:"80",ease:"circ.out"}),e(n).hasClass("split-in-down")&&gsap.set(n.split.chars,{opacity:0,y:"-80",ease:"circ.out"}),e(n).hasClass("split-in-rotate")&&gsap.set(n.split.chars,{opacity:0,rotateX:"50deg",ease:"circ.out"}),e(n).hasClass("split-in-scale")&&gsap.set(n.split.chars,{opacity:0,scale:"0.5",ease:"circ.out"}),n.anim=gsap.to(n.split.chars,{scrollTrigger:{trigger:n,toggleActions:"restart pause resume reverse",start:"top 90%"},x:"0",y:"0",rotateX:"0",scale:1,opacity:1,duration:.8,stagger:.02})})},200)}function s(t){t.find(".pxl-heading__title.pxl-heading__style-scroll-bg").each(function(){var t=e(this).find(".pxl-heading__text"),n=new SplitText(t[0],{type:"words, chars"});e(n.words).children().first().addClass("first-char"),gsap.fromTo(n.chars,{position:"relative",display:"inline-block",opacity:.2,x:-5},{opacity:1,x:0,stagger:.1,scrollTrigger:{trigger:t[0],toggleActions:"play pause reverse pause",start:"top 70%",end:"top 40%",scrub:.7}})})}function o(t){t.find(".pxl-heading__title.pxl-heading__style-scroll-line").each(function(){var t=e(this).find(".pxl-heading__text");const n=new SplitText(t[0],{type:"lines",linesClass:"split-line"});gsap.fromTo(n.lines,{position:"relative",display:"inline-block",opacity:.2,x:-5},{opacity:1,x:0,stagger:.1,scrollTrigger:{trigger:t[0],toggleActions:"play pause reverse pause",start:"top 70%",end:"top 40%",scrub:.7}})})}function i(t){const n=async()=>{const e=await fetch("https://api.coingecko.com/api/v3/coins/markets?vs_currency=usd&order=market_cap_desc&per_page=300&page=1&sparkline=false");return await e.json()};n().then(function(n){const s=n.length;t.find(".pxl-marquee--coin").each(function(){const t=e(this),o=parseInt(t.data("list-number")),i=Math.ceil(s/o);for(let e=0;e<o;e++){const r=t.find(`.pxl-marquee__list--${e}`),a=e*i,c=Math.min(a+i,s),l=n.slice(a,c);l.forEach(function(e){const t=`
              <li class="pxl-marquee__item">
                <img src="${e.image}" alt="${e.name}" />
                <span class="pxl-marquee__item-wrapper">
                  <span class="pxl-marquee__item-name">${e.name}</span>
                  <span class="pxl-marquee__item-price-wrapper">
                    <span class="pxl-marquee__item-price">${e.current_price}$</span>
                    <span class="pxl-marquee__item-percent ${e.price_change_percentage_24h>0?"increase":"decrease"}">
                      ${e.price_change_percentage_24h.toFixed(2)}%
                    </span>
                  </span>
                </span>
              </li>
            `;r.append(t)})}})})}function a(t){t.find(".pxl-heading__title.pxl-heading__style-scroll-gradient").each(function(){var t=e(this).find(".pxl-heading__text");const n=new SplitText(t[0],{type:"lines",linesClass:"split-line"});t.hasClass("pxl-heading__has-quote")&&t.append(`
          <svg xmlns="http://www.w3.org/2000/svg" width="40" height="41" viewBox="0 0 40 41" fill="none" class="quote-svg quote-svg-left">
            <path d="M40 17.7298L31.4286 17.7298L37.1429 6.30127L28.5715 6.30127L22.8572 17.7298L22.8572 34.8726H40L40 17.7298Z" fill="white"/>
            <path d="M17.1428 34.8727L17.1428 17.7298L8.57142 17.7298L14.2857 6.3013L5.71433 6.3013L1.49867e-06 17.7298L0 34.8727L17.1428 34.8727Z" fill="white"/>
          </svg>
          <svg xmlns="http://www.w3.org/2000/svg" width="40" height="41" viewBox="0 0 40 41" fill="none" class="quote-svg quote-svg-right">
            <path d="M0 23.4441H8.57142L2.8571 34.8727H11.4285L17.1428 23.4441V6.3013H0V23.4441Z" fill="url(#paint0_linear_3714_7182)"/>
            <path d="M22.8572 6.30127V23.4441H31.4286L25.7143 34.8726H34.2857L40 23.4441V6.30127H22.8572Z" fill="url(#paint1_linear_3714_7182)"/>
            <defs>
              <linearGradient id="paint0_linear_3714_7182" x1="40" y1="34.5869" x2="1.34047e-07" y2="6.58692" gradientUnits="userSpaceOnUse">
                <stop stop-color="#A7FFA4"/>
                <stop offset="1" stop-color="white"/>
              </linearGradient>
              <linearGradient id="paint1_linear_3714_7182" x1="40" y1="34.5869" x2="1.34047e-07" y2="6.58692" gradientUnits="userSpaceOnUse">
                <stop stop-color="#A7FFA4"/>
                <stop offset="1" stop-color="white"/>
              </linearGradient>
            </defs>
          </svg>
      `);let s=gsap.timeline({scrollTrigger:{trigger:t[0],toggleActions:"play pause reverse pause",start:"top 70%",end:"top 40%",scrub:.7}});n.lines.forEach(e=>{s.fromTo(e,{backgroundPosition:"100% 100%"},{backgroundPosition:"0% 100%",duration:.5,ease:"none"},">0")})})}var r=function(e){const t=e[0]?.querySelector(".pxl-physics");if(!t)return;gsap.registerPlugin(ScrollTrigger),ScrollTrigger.create({trigger:t,start:"top 80%",once:!0,onEnter:()=>{c(t)}})};function c(e){const{Engine:y,Render:c,Runner:l,Bodies:o,Composite:_,MouseConstraint:h,Events:b}=Matter;let u=[];try{u=JSON.parse(e.getAttribute("data-settings").replace(/&quot;/g,'"'))}catch{return}const t=e.offsetWidth,n=e.offsetHeight,s=y.create();s.world.gravity.y=.7;const m=c.create({element:e,engine:s,options:{width:t,height:n,background:"rgba(0,0,0,0)",wireframes:!1,pixelRatio:window.devicePixelRatio}}),i={isStatic:!0,render:{visible:!1}},f=o.rectangle(t/2,-10,t,10,i),p=o.rectangle(t/2,n+10,t,10,i),g=o.rectangle(-10,n/2,10,n,i),v=o.rectangle(t+10,n/2,10,n,i),d=["#fdf49c","#c6f0c0","#f8c7ec","#c1e8f9","#e2dbf7","#ffd1a9","#d3f9d8","#e3f2fd","#fbe3e8","#f5f5dc"],j=[{x:80,y:50,angle:-.5},{x:220,y:55,angle:.1},{x:145,y:105,angle:-.7},{x:215,y:135,angle:.3},{x:85,y:145,angle:0}],r=[];u.forEach((t,n)=>{const{x:i,y:a,angle:c}=j[n]||{x:100,y:100,angle:0},s=document.createElement("p");s.className="pxl-throwable-element",Object.assign(s.style,{position:"absolute",opacity:"1",borderRadius:"9999px",fontSize:"14px",fontWeight:"600",color:"black",textAlign:"center",padding:"10px 22px",lineHeight:"1.2",pointerEvents:"none",userSelect:"none",whiteSpace:"nowrap",backgroundColor:d[n%d.length],margin:"0"}),s.innerText=t,e.appendChild(s);const{width:l,height:u}=s.getBoundingClientRect(),h=o.rectangle(i,a,l,u,{angle:c,restitution:.3,friction:.8,render:{visible:!1}});r.push({body:h,element:s})});const a=h.create(s,{element:e,constraint:{render:{visible:!1}}});window.addEventListener("mouseup",()=>{a.mouse.button=-1}),window.addEventListener("blur",()=>{a.mouse.button=-1}),_.add(s.world,[f,p,g,v,a,...r.map(e=>e.body)]),c.run(m),l.run(l.create(),s),b.on(s,"afterUpdate",()=>{r.forEach(({body:e,element:t})=>{t.style.left=`${e.position.x}px`,t.style.top=`${e.position.y}px`,t.style.transform=`translate(-50%, -50%) rotate(${e.angle}rad)`})})}class l{constructor(){this.activeContexts=new Map,this.maxContexts=4}createContext(e,t="2d"){const n=e.dataset.canvasId||this.generateId();e.dataset.canvasId=n,this.cleanup(n),this.activeContexts.size>=this.maxContexts&&this.cleanupOldest();const s=e.getContext(t,{alpha:!0,antialias:!1,depth:!1,stencil:!1,preserveDrawingBuffer:!1,powerPreference:"low-power"});return s&&this.activeContexts.set(n,{canvas:e,context:s,type:t,created:Date.now()}),s}cleanup(e){const t=this.activeContexts.get(e);if(t){const{canvas:n,context:o,type:s}=t;if(s==="webgl"||s==="webgl2"){const e=o.getExtension("WEBGL_lose_context");e&&e.loseContext()}n.width=1,n.height=1,this.activeContexts.delete(e)}}cleanupOldest(){if(this.activeContexts.size===0)return;let e=null,t=Date.now();for(const[s,n]of this.activeContexts)n.created<t&&(t=n.created,e=s);e&&this.cleanup(e)}cleanupAll(){for(const e of this.activeContexts.keys())this.cleanup(e)}generateId(){return"canvas_"+Math.random().toString(36).substr(2,9)}}const t=new l;function d(){t.cleanupAll();const i=()=>window.innerWidth<768||navigator.hardwareConcurrency<4||!window.requestAnimationFrame;if(i())return;const n=new Set;let s=null;function a(e){const i=e[0],l=t.generateId();i.dataset.canvasId=l;const a=t.createContext(i,"2d");if(!a)return null;let r=0,c=0,d=[],w=-9999,b=-9999,u=!0;const p=parseInt(e.data("star"))||60,g=new IntersectionObserver(e=>{e.forEach(e=>{const t=u;u=e.isIntersecting,u&&!t?(n.add(l),s||(s=requestAnimationFrame(o))):!u&&t&&n.delete(l)})},{threshold:.1,rootMargin:"50px"});g.observe(i);let h;function v(){clearTimeout(h),h=setTimeout(()=>{i.parentNode&&(gsap.set(i,{autoAlpha:0}),f(i,()=>{j(),_(p),m(),requestAnimationFrame(()=>{gsap.to(i,{autoAlpha:1,duration:2,ease:"power2.out"})})}))},250)}function f(e,t){let s=0;const i=10;let n=e.getBoundingClientRect();function o(){const a=e.getBoundingClientRect(),r=Math.abs(a.width-n.width)<1&&Math.abs(a.height-n.height)<1;r||s>=i?t():(n=a,s++,requestAnimationFrame(o))}requestAnimationFrame(o)}function j(){const s=i.getBoundingClientRect(),n=Math.min(window.devicePixelRatio||1,1.5);let e=Math.min(s.width,1920),t=Math.min(s.height,1080);(e<10||t<10)&&(e=300,t=150),i.width=e*n,i.height=t*n,i.style.width=e+"px",i.style.height=t+"px",a.setTransform(1,0,0,1,0,0),a.scale(n,n),r=e,c=t}function y(e,t){const n=Math.random()*2*Math.PI,s=.05+Math.random()*.1;return{x:e,y:t,vx:Math.cos(n)*s,vy:Math.sin(n)*s,radius:.5+Math.random()*1,phase:Math.random()*Math.PI*2,baseOpacity:.3+Math.random()*.4,opacitySpeed:.5+Math.random()*.8}}function _(e){d=[];for(let t=0;t<e;t++)d.push(y(Math.random()*r,Math.random()*c))}function m(){if(!u||!a||r===0||c===0)return;a.clearRect(0,0,r,c);const s=performance.now()*.001,t=e.data("color")||"#ffffff",n=(e,t)=>{if(!e)return`rgba(255,255,255,${t})`;e[0]==="#"&&(e=e.substring(1));const n=parseInt(e.substring(0,2),16),s=parseInt(e.substring(2,4),16),o=parseInt(e.substring(4,6),16);return`rgba(${n},${s},${o},${t})`};for(d=d.filter(e=>e.x>=-10&&e.x<=r+10&&e.y>=-10&&e.y<=c+10);d.length<p;)d.push(y(Math.random()*r,Math.random()*c));d.forEach(e=>{const f=Math.sin(s*e.opacitySpeed+e.phase),p=Math.max(0,Math.min(1,e.baseOpacity+f*.2));e.x+=e.vx,e.y+=e.vy;const l=w-e.x,i=b-e.y,d=l*l+i*i,h=100*100;if(d<h&&d>1){const t=(1-d/h)*.008;e.vx+=l*t,e.vy+=i*t,e.vx*=.99,e.vy*=.99}e.x<-5&&(e.x=r+5),e.x>r+5&&(e.x=-5),e.y<-5&&(e.y=c+5),e.y>c+5&&(e.y=-5);const o=80,g=Math.max(0,o-e.x),v=Math.max(0,e.x-(r-o)),j=Math.max(0,o-e.y),y=Math.max(0,e.y-(c-o)),m=Math.max(g,v,j,y),_=m>0?Math.max(0,Math.pow(1-m/o,2)):1,u=p*_;if(a.fillStyle=n(t,u),a.beginPath(),a.arc(e.x,e.y,e.radius,0,Math.PI*2),a.fill(),u>.3){const s=u*.2;a.fillStyle=n(t,s),a.beginPath(),a.arc(e.x,e.y,e.radius*1.5,0,Math.PI*2),a.fill()}})}i.addEventListener("mouseleave",()=>{w=-9999,b=-9999},{passive:!0}),window.addEventListener("resize",v,{passive:!0}),f(i,()=>{gsap.set(i,{autoAlpha:0}),f(i,()=>{j(),_(p),m(),requestAnimationFrame(()=>{gsap.to(i,{autoAlpha:1,duration:2,ease:"power2.out"})})}),requestAnimationFrame(()=>{})}),n.add(l);function O(){g.disconnect(),n.delete(l),t.cleanup(l),window.removeEventListener("resize",v),clearTimeout(h)}const x=new MutationObserver(e=>{e.forEach(e=>{e.removedNodes.forEach(e=>{(e===i||e.contains&&e.contains(i))&&O()})})});return x.observe(document.body,{childList:!0,subtree:!0}),{update:m,cleanup:O,id:l}}function o(){if(n.size===0){s=null;return}e(".pxl-star").each(function(){const t=this.dataset.canvasId;if(n.has(t)){const t=e(this).data("effectInstance");t&&t.update&&t.update()}}),s=requestAnimationFrame(o)}e(".pxl-star").each(function(){const t=e(this),n=t.data("effectInstance");n&&n.cleanup&&n.cleanup();const s=a(t);s&&t.data("effectInstance",s)}),n.size>0&&!s&&(s=requestAnimationFrame(o))}function u(t){t.find(".pxl-heading").each(function(){var t=e(this).data("animation-type")||"";e(this).animatedHeadline({animationType:t})})}function h(t){var n=Math.random().toString(36).substring(2,15);t.find(".pxl-heading__highlight-line-bottom").each(function(){const t=`
        <svg xmlns="http://www.w3.org/2000/svg" width="211" height="15" viewBox="0 0 211 15" fill="none">
          <path d="M2.33889 14.9788C55.5174 7.83478 109.179 3.58158 163.16 3.4522C178.528 3.41466 193.858 3.62154 209.224 3.884C210.876 3.92981 210.942 2.02586 209.315 1.90576C181.932 -0.114952 154.17 -0.400643 126.703 0.450308C99.7006 1.27806 72.7749 3.22863 46.0425 6.2936C31.1432 8.01123 16.3198 10.0855 1.63537 12.6785C-0.289549 13.0206 0.426526 15.2394 2.33889 14.9788Z" fill="url(#paint0_linear_3803_8970)"/>
          <defs>
          <linearGradient id="paint0_linear_3803_8970_${n}" x1="0.5" y1="7.5" x2="210.5" y2="7.5" gradientUnits="userSpaceOnUse">
          <stop stop-color="#CF208B"/>
          <stop offset="1" stop-color="#791599"/>
          </linearGradient>
          </defs>
        </svg>`;e(this).find(".pxl-heading__highlight").append(t),e(this).find(".pxl-heading__highlight svg path").attr("fill",`url(#paint0_linear_3803_8970_${n})`)})}e(window).on("beforeunload",()=>{t.cleanupAll()}),e(window).on("elementor/frontend/init",function(){elementorFrontend.hooks.addAction("frontend/element_ready/pxl_heading.default",function(e){n(e),s(e),o(e),a(e),u(e),h(e)}),elementorFrontend.hooks.addAction("frontend/element_ready/pxl_physics.default",r),elementorFrontend.hooks.addAction("frontend/element_ready/pxl_coin_marquee.default",i),elementorFrontend.hooks.addAction("frontend/element_ready/global",function(){setTimeout(()=>{window.matchMedia("(prefers-reduced-motion: reduce)").matches||d()},300)})})})(jQuery)