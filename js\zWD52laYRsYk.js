/*! This file is auto-generated */
!function(n,t){var r,e;"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define("underscore",t):(n="undefined"!=typeof globalThis?globalThis:n||self,r=n._,(e=n._=t()).noConflict=function(){return n._=r,e})}(this,function(){var n="1.13.7",t="object"==typeof self&&self.self===self&&self||"object"==typeof global&&global.global===global&&global||Function("return this")()||{},e=Array.prototype,V=Object.prototype,F="undefined"!=typeof Symbol?Symbol.prototype:null,P=e.push,f=e.slice,s=V.toString,q=V.hasOwnProperty,r="undefined"!=typeof ArrayBuffer,u="undefined"!=typeof DataView,U=Array.isArray,W=Object.keys,z=Object.create,L=r&&ArrayBuffer.isView,$=isNaN,C=isFinite,K=!{toString:null}.propertyIsEnumerable("toString"),J=["valueOf","isPrototypeOf","toString","propertyIsEnumerable","hasOwnProperty","toLocaleString"],G=Math.pow(2,53)-1;function l(u,o){return o=null==o?u.length-1:+o,function(){for(var n=Math.max(arguments.length-o,0),t=Array(n),r=0;r<n;r++)t[r]=arguments[r+o];switch(o){case 0:return u.call(this,t);case 1:return u.call(this,arguments[0],t);case 2:return u.call(this,arguments[0],arguments[1],t)}for(var e=Array(o+1),r=0;r<o;r++)e[r]=arguments[r];return e[o]=t,u.apply(this,e)}}function o(n){var t=typeof n;return"function"==t||"object"==t&&!!n}function H(n){return void 0===n}function Q(n){return!0===n||!1===n||"[object Boolean]"===s.call(n)}function i(n){var t="[object "+n+"]";return function(n){return s.call(n)===t}}var X=i("String"),Y=i("Number"),Z=i("Date"),nn=i("RegExp"),tn=i("Error"),rn=i("Symbol"),en=i("ArrayBuffer"),a=i("Function"),t=t.document&&t.document.childNodes,p=a="function"!=typeof/./&&"object"!=typeof Int8Array&&"function"!=typeof t?function(n){return"function"==typeof n||!1}:a,t=i("Object"),un=u&&(!/\[native code\]/.test(String(DataView))||t(new DataView(new ArrayBuffer(8)))),a="undefined"!=typeof Map&&t(new Map),u=i("DataView");var h=un?function(n){return null!=n&&p(n.getInt8)&&en(n.buffer)}:u,v=U||i("Array");function y(n,t){return null!=n&&q.call(n,t)}var on=i("Arguments"),an=(!function(){on(arguments)||(on=function(n){return y(n,"callee")})}(),on);function fn(n){return Y(n)&&$(n)}function cn(n){return function(){return n}}function ln(t){return function(n){n=t(n);return"number"==typeof n&&0<=n&&n<=G}}function sn(t){return function(n){return null==n?void 0:n[t]}}var d=sn("byteLength"),pn=ln(d),hn=/\[object ((I|Ui)nt(8|16|32)|Float(32|64)|Uint8Clamped|Big(I|Ui)nt64)Array\]/;var vn=r?function(n){return L?L(n)&&!h(n):pn(n)&&hn.test(s.call(n))}:cn(!1),g=sn("length");function yn(n,t){t=function(t){for(var r={},n=t.length,e=0;e<n;++e)r[t[e]]=!0;return{contains:function(n){return!0===r[n]},push:function(n){return r[n]=!0,t.push(n)}}}(t);var r=J.length,e=n.constructor,u=p(e)&&e.prototype||V,o="constructor";for(y(n,o)&&!t.contains(o)&&t.push(o);r--;)(o=J[r])in n&&n[o]!==u[o]&&!t.contains(o)&&t.push(o)}function b(n){if(!o(n))return[];if(W)return W(n);var t,r=[];for(t in n)y(n,t)&&r.push(t);return K&&yn(n,r),r}function dn(n,t){var r=b(t),e=r.length;if(null==n)return!e;for(var u=Object(n),o=0;o<e;o++){var i=r[o];if(t[i]!==u[i]||!(i in u))return!1}return!0}function m(n){return n instanceof m?n:this instanceof m?void(this._wrapped=n):new m(n)}function gn(n){return new Uint8Array(n.buffer||n,n.byteOffset||0,d(n))}m.VERSION=n,m.prototype.valueOf=m.prototype.toJSON=m.prototype.value=function(){return this._wrapped},m.prototype.toString=function(){return String(this._wrapped)};var bn="[object DataView]";function mn(n,t,r,e){var u;return n===t?0!==n||1/n==1/t:null!=n&&null!=t&&(n!=n?t!=t:("function"==(u=typeof n)||"object"==u||"object"==typeof t)&&function n(t,r,e,u){t instanceof m&&(t=t._wrapped);r instanceof m&&(r=r._wrapped);var o=s.call(t);if(o!==s.call(r))return!1;if(un&&"[object Object]"==o&&h(t)){if(!h(r))return!1;o=bn}switch(o){case"[object RegExp]":case"[object String]":return""+t==""+r;case"[object Number]":return+t!=+t?+r!=+r:0==+t?1/+t==1/r:+t==+r;case"[object Date]":case"[object Boolean]":return+t==+r;case"[object Symbol]":return F.valueOf.call(t)===F.valueOf.call(r);case"[object ArrayBuffer]":case bn:return n(gn(t),gn(r),e,u)}o="[object Array]"===o;if(!o&&vn(t)){var i=d(t);if(i!==d(r))return!1;if(t.buffer===r.buffer&&t.byteOffset===r.byteOffset)return!0;o=!0}if(!o){if("object"!=typeof t||"object"!=typeof r)return!1;var i=t.constructor,a=r.constructor;if(i!==a&&!(p(i)&&i instanceof i&&p(a)&&a instanceof a)&&"constructor"in t&&"constructor"in r)return!1}e=e||[];u=u||[];var f=e.length;for(;f--;)if(e[f]===t)return u[f]===r;e.push(t);u.push(r);if(o){if((f=t.length)!==r.length)return!1;for(;f--;)if(!mn(t[f],r[f],e,u))return!1}else{var c,l=b(t);if(f=l.length,b(r).length!==f)return!1;for(;f--;)if(c=l[f],!y(r,c)||!mn(t[c],r[c],e,u))return!1}e.pop();u.pop();return!0}(n,t,r,e))}function c(n){if(!o(n))return[];var t,r=[];for(t in n)r.push(t);return K&&yn(n,r),r}function jn(e){var u=g(e);return function(n){if(null==n)return!1;var t=c(n);if(g(t))return!1;for(var r=0;r<u;r++)if(!p(n[e[r]]))return!1;return e!==_n||!p(n[wn])}}var wn="forEach",t=["clear","delete"],u=["get","has","set"],U=t.concat(wn,u),_n=t.concat(u),r=["add"].concat(t,wn,"has"),u=a?jn(U):i("Map"),t=a?jn(_n):i("WeakMap"),U=a?jn(r):i("Set"),a=i("WeakSet");function j(n){for(var t=b(n),r=t.length,e=Array(r),u=0;u<r;u++)e[u]=n[t[u]];return e}function An(n){for(var t={},r=b(n),e=0,u=r.length;e<u;e++)t[n[r[e]]]=r[e];return t}function xn(n){var t,r=[];for(t in n)p(n[t])&&r.push(t);return r.sort()}function Sn(f,c){return function(n){var t=arguments.length;if(c&&(n=Object(n)),!(t<2||null==n))for(var r=1;r<t;r++)for(var e=arguments[r],u=f(e),o=u.length,i=0;i<o;i++){var a=u[i];c&&void 0!==n[a]||(n[a]=e[a])}return n}}var On=Sn(c),w=Sn(b),Mn=Sn(c,!0);function En(n){var t;return o(n)?z?z(n):((t=function(){}).prototype=n,n=new t,t.prototype=null,n):{}}function Bn(n){return v(n)?n:[n]}function _(n){return m.toPath(n)}function Nn(n,t){for(var r=t.length,e=0;e<r;e++){if(null==n)return;n=n[t[e]]}return r?n:void 0}function In(n,t,r){n=Nn(n,_(t));return H(n)?r:n}function Tn(n){return n}function A(t){return t=w({},t),function(n){return dn(n,t)}}function kn(t){return t=_(t),function(n){return Nn(n,t)}}function x(u,o,n){if(void 0===o)return u;switch(null==n?3:n){case 1:return function(n){return u.call(o,n)};case 3:return function(n,t,r){return u.call(o,n,t,r)};case 4:return function(n,t,r,e){return u.call(o,n,t,r,e)}}return function(){return u.apply(o,arguments)}}function Dn(n,t,r){return null==n?Tn:p(n)?x(n,t,r):(o(n)&&!v(n)?A:kn)(n)}function Rn(n,t){return Dn(n,t,1/0)}function S(n,t,r){return m.iteratee!==Rn?m.iteratee(n,t):Dn(n,t,r)}function Vn(){}function Fn(n,t){return null==t&&(t=n,n=0),n+Math.floor(Math.random()*(t-n+1))}m.toPath=Bn,m.iteratee=Rn;var O=Date.now||function(){return(new Date).getTime()};function Pn(t){function r(n){return t[n]}var n="(?:"+b(t).join("|")+")",e=RegExp(n),u=RegExp(n,"g");return function(n){return e.test(n=null==n?"":""+n)?n.replace(u,r):n}}var r={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;"},qn=Pn(r),r=Pn(An(r)),Un=m.templateSettings={evaluate:/<%([\s\S]+?)%>/g,interpolate:/<%=([\s\S]+?)%>/g,escape:/<%-([\s\S]+?)%>/g},Wn=/(.)^/,zn={"'":"'","\\":"\\","\r":"r","\n":"n","\u2028":"u2028","\u2029":"u2029"},Ln=/\\|'|\r|\n|\u2028|\u2029/g;function $n(n){return"\\"+zn[n]}var Cn=/^\s*(\w|\$)+\s*$/;var Kn=0;function Jn(n,t,r,e,u){return e instanceof t?(e=En(n.prototype),o(t=n.apply(e,u))?t:e):n.apply(r,u)}var M=l(function(u,o){function i(){for(var n=0,t=o.length,r=Array(t),e=0;e<t;e++)r[e]=o[e]===a?arguments[n++]:o[e];for(;n<arguments.length;)r.push(arguments[n++]);return Jn(u,i,this,this,r)}var a=M.placeholder;return i}),Gn=(M.placeholder=m,l(function(t,r,e){var u;if(p(t))return u=l(function(n){return Jn(t,u,r,this,e.concat(n))});throw new TypeError("Bind must be called on a function")})),E=ln(g);function B(n,t,r,e){if(e=e||[],t||0===t){if(t<=0)return e.concat(n)}else t=1/0;for(var u=e.length,o=0,i=g(n);o<i;o++){var a=n[o];if(E(a)&&(v(a)||an(a)))if(1<t)B(a,t-1,r,e),u=e.length;else for(var f=0,c=a.length;f<c;)e[u++]=a[f++];else r||(e[u++]=a)}return e}var Hn=l(function(n,t){var r=(t=B(t,!1,!1)).length;if(r<1)throw new Error("bindAll must be passed function names");for(;r--;){var e=t[r];n[e]=Gn(n[e],n)}return n});var Qn=l(function(n,t,r){return setTimeout(function(){return n.apply(null,r)},t)}),Xn=M(Qn,m,1);function Yn(n){return function(){return!n.apply(this,arguments)}}function Zn(n,t){var r;return function(){return 0<--n&&(r=t.apply(this,arguments)),n<=1&&(t=null),r}}var nt=M(Zn,2);function tt(n,t,r){t=S(t,r);for(var e,u=b(n),o=0,i=u.length;o<i;o++)if(t(n[e=u[o]],e,n))return e}function rt(o){return function(n,t,r){t=S(t,r);for(var e=g(n),u=0<o?0:e-1;0<=u&&u<e;u+=o)if(t(n[u],u,n))return u;return-1}}var et=rt(1),ut=rt(-1);function ot(n,t,r,e){for(var u=(r=S(r,e,1))(t),o=0,i=g(n);o<i;){var a=Math.floor((o+i)/2);r(n[a])<u?o=a+1:i=a}return o}function it(o,i,a){return function(n,t,r){var e=0,u=g(n);if("number"==typeof r)0<o?e=0<=r?r:Math.max(r+u,e):u=0<=r?Math.min(r+1,u):r+u+1;else if(a&&r&&u)return n[r=a(n,t)]===t?r:-1;if(t!=t)return 0<=(r=i(f.call(n,e,u),fn))?r+e:-1;for(r=0<o?e:u-1;0<=r&&r<u;r+=o)if(n[r]===t)return r;return-1}}var at=it(1,et,ot),ft=it(-1,ut);function ct(n,t,r){t=(E(n)?et:tt)(n,t,r);if(void 0!==t&&-1!==t)return n[t]}function N(n,t,r){if(t=x(t,r),E(n))for(u=0,o=n.length;u<o;u++)t(n[u],u,n);else for(var e=b(n),u=0,o=e.length;u<o;u++)t(n[e[u]],e[u],n);return n}function I(n,t,r){t=S(t,r);for(var e=!E(n)&&b(n),u=(e||n).length,o=Array(u),i=0;i<u;i++){var a=e?e[i]:i;o[i]=t(n[a],a,n)}return o}function lt(f){return function(n,t,r,e){var u=3<=arguments.length;return function(n,t,r,e){var u=!E(n)&&b(n),o=(u||n).length,i=0<f?0:o-1;for(e||(r=n[u?u[i]:i],i+=f);0<=i&&i<o;i+=f){var a=u?u[i]:i;r=t(r,n[a],a,n)}return r}(n,x(t,e,4),r,u)}}var st=lt(1),pt=lt(-1);function T(n,e,t){var u=[];return e=S(e,t),N(n,function(n,t,r){e(n,t,r)&&u.push(n)}),u}function ht(n,t,r){t=S(t,r);for(var e=!E(n)&&b(n),u=(e||n).length,o=0;o<u;o++){var i=e?e[o]:o;if(!t(n[i],i,n))return!1}return!0}function vt(n,t,r){t=S(t,r);for(var e=!E(n)&&b(n),u=(e||n).length,o=0;o<u;o++){var i=e?e[o]:o;if(t(n[i],i,n))return!0}return!1}function k(n,t,r,e){return E(n)||(n=j(n)),0<=at(n,t,r="number"==typeof r&&!e?r:0)}var yt=l(function(n,r,e){var u,o;return p(r)?o=r:(r=_(r),u=r.slice(0,-1),r=r[r.length-1]),I(n,function(n){var t=o;if(!t){if(null==(n=u&&u.length?Nn(n,u):n))return;t=n[r]}return null==t?t:t.apply(n,e)})});function dt(n,t){return I(n,kn(t))}function gt(n,e,t){var r,u,o=-1/0,i=-1/0;if(null==e||"number"==typeof e&&"object"!=typeof n[0]&&null!=n)for(var a=0,f=(n=E(n)?n:j(n)).length;a<f;a++)null!=(r=n[a])&&o<r&&(o=r);else e=S(e,t),N(n,function(n,t,r){u=e(n,t,r),(i<u||u===-1/0&&o===-1/0)&&(o=n,i=u)});return o}var bt=/[^\ud800-\udfff]|[\ud800-\udbff][\udc00-\udfff]|[\ud800-\udfff]/g;function mt(n){return n?v(n)?f.call(n):X(n)?n.match(bt):E(n)?I(n,Tn):j(n):[]}function jt(n,t,r){if(null==t||r)return(n=E(n)?n:j(n))[Fn(n.length-1)];for(var e=mt(n),r=g(e),u=(t=Math.max(Math.min(t,r),0),r-1),o=0;o<t;o++){var i=Fn(o,u),a=e[o];e[o]=e[i],e[i]=a}return e.slice(0,t)}function D(o,t){return function(r,e,n){var u=t?[[],[]]:{};return e=S(e,n),N(r,function(n,t){t=e(n,t,r);o(u,n,t)}),u}}var wt=D(function(n,t,r){y(n,r)?n[r].push(t):n[r]=[t]}),_t=D(function(n,t,r){n[r]=t}),At=D(function(n,t,r){y(n,r)?n[r]++:n[r]=1}),xt=D(function(n,t,r){n[r?0:1].push(t)},!0);function St(n,t,r){return t in r}var Ot=l(function(n,t){var r={},e=t[0];if(null!=n){p(e)?(1<t.length&&(e=x(e,t[1])),t=c(n)):(e=St,t=B(t,!1,!1),n=Object(n));for(var u=0,o=t.length;u<o;u++){var i=t[u],a=n[i];e(a,i,n)&&(r[i]=a)}}return r}),Mt=l(function(n,r){var t,e=r[0];return p(e)?(e=Yn(e),1<r.length&&(t=r[1])):(r=I(B(r,!1,!1),String),e=function(n,t){return!k(r,t)}),Ot(n,e,t)});function Et(n,t,r){return f.call(n,0,Math.max(0,n.length-(null==t||r?1:t)))}function Bt(n,t,r){return null==n||n.length<1?null==t||r?void 0:[]:null==t||r?n[0]:Et(n,n.length-t)}function R(n,t,r){return f.call(n,null==t||r?1:t)}var Nt=l(function(n,t){return t=B(t,!0,!0),T(n,function(n){return!k(t,n)})}),It=l(function(n,t){return Nt(n,t)});function Tt(n,t,r,e){Q(t)||(e=r,r=t,t=!1),null!=r&&(r=S(r,e));for(var u=[],o=[],i=0,a=g(n);i<a;i++){var f=n[i],c=r?r(f,i,n):f;t&&!r?(i&&o===c||u.push(f),o=c):r?k(o,c)||(o.push(c),u.push(f)):k(u,f)||u.push(f)}return u}var kt=l(function(n){return Tt(B(n,!0,!0))});function Dt(n){for(var t=n&&gt(n,g).length||0,r=Array(t),e=0;e<t;e++)r[e]=dt(n,e);return r}var Rt=l(Dt);function Vt(n,t){return n._chain?m(t).chain():t}function Ft(r){return N(xn(r),function(n){var t=m[n]=r[n];m.prototype[n]=function(){var n=[this._wrapped];return P.apply(n,arguments),Vt(this,t.apply(m,n))}}),m}N(["pop","push","reverse","shift","sort","splice","unshift"],function(t){var r=e[t];m.prototype[t]=function(){var n=this._wrapped;return null!=n&&(r.apply(n,arguments),"shift"!==t&&"splice"!==t||0!==n.length||delete n[0]),Vt(this,n)}}),N(["concat","join","slice"],function(n){var t=e[n];m.prototype[n]=function(){var n=this._wrapped;return Vt(this,n=null!=n?t.apply(n,arguments):n)}});n=Ft({__proto__:null,VERSION:n,restArguments:l,isObject:o,isNull:function(n){return null===n},isUndefined:H,isBoolean:Q,isElement:function(n){return!(!n||1!==n.nodeType)},isString:X,isNumber:Y,isDate:Z,isRegExp:nn,isError:tn,isSymbol:rn,isArrayBuffer:en,isDataView:h,isArray:v,isFunction:p,isArguments:an,isFinite:function(n){return!rn(n)&&C(n)&&!isNaN(parseFloat(n))},isNaN:fn,isTypedArray:vn,isEmpty:function(n){var t;return null==n||("number"==typeof(t=g(n))&&(v(n)||X(n)||an(n))?0===t:0===g(b(n)))},isMatch:dn,isEqual:function(n,t){return mn(n,t)},isMap:u,isWeakMap:t,isSet:U,isWeakSet:a,keys:b,allKeys:c,values:j,pairs:function(n){for(var t=b(n),r=t.length,e=Array(r),u=0;u<r;u++)e[u]=[t[u],n[t[u]]];return e},invert:An,functions:xn,methods:xn,extend:On,extendOwn:w,assign:w,defaults:Mn,create:function(n,t){return n=En(n),t&&w(n,t),n},clone:function(n){return o(n)?v(n)?n.slice():On({},n):n},tap:function(n,t){return t(n),n},get:In,has:function(n,t){for(var r=(t=_(t)).length,e=0;e<r;e++){var u=t[e];if(!y(n,u))return!1;n=n[u]}return!!r},mapObject:function(n,t,r){t=S(t,r);for(var e=b(n),u=e.length,o={},i=0;i<u;i++){var a=e[i];o[a]=t(n[a],a,n)}return o},identity:Tn,constant:cn,noop:Vn,toPath:Bn,property:kn,propertyOf:function(t){return null==t?Vn:function(n){return In(t,n)}},matcher:A,matches:A,times:function(n,t,r){var e=Array(Math.max(0,n));t=x(t,r,1);for(var u=0;u<n;u++)e[u]=t(u);return e},random:Fn,now:O,escape:qn,unescape:r,templateSettings:Un,template:function(o,n,t){n=Mn({},n=!n&&t?t:n,m.templateSettings);var r,t=RegExp([(n.escape||Wn).source,(n.interpolate||Wn).source,(n.evaluate||Wn).source].join("|")+"|$","g"),i=0,a="__p+='";if(o.replace(t,function(n,t,r,e,u){return a+=o.slice(i,u).replace(Ln,$n),i=u+n.length,t?a+="'+\n((__t=("+t+"))==null?'':_.escape(__t))+\n'":r?a+="'+\n((__t=("+r+"))==null?'':__t)+\n'":e&&(a+="';\n"+e+"\n__p+='"),n}),a+="';\n",t=n.variable){if(!Cn.test(t))throw new Error("variable is not a bare identifier: "+t)}else a="with(obj||{}){\n"+a+"}\n",t="obj";a="var __t,__p='',__j=Array.prototype.join,print=function(){__p+=__j.call(arguments,'');};\n"+a+"return __p;\n";try{r=new Function(t,"_",a)}catch(n){throw n.source=a,n}function e(n){return r.call(this,n,m)}return e.source="function("+t+"){\n"+a+"}",e},result:function(n,t,r){var e=(t=_(t)).length;if(!e)return p(r)?r.call(n):r;for(var u=0;u<e;u++){var o=null==n?void 0:n[t[u]];void 0===o&&(o=r,u=e),n=p(o)?o.call(n):o}return n},uniqueId:function(n){var t=++Kn+"";return n?n+t:t},chain:function(n){return(n=m(n))._chain=!0,n},iteratee:Rn,partial:M,bind:Gn,bindAll:Hn,memoize:function(e,u){function o(n){var t=o.cache,r=""+(u?u.apply(this,arguments):n);return y(t,r)||(t[r]=e.apply(this,arguments)),t[r]}return o.cache={},o},delay:Qn,defer:Xn,throttle:function(r,e,u){function o(){l=!1===u.leading?0:O(),i=null,c=r.apply(a,f),i||(a=f=null)}function n(){var n=O(),t=(l||!1!==u.leading||(l=n),e-(n-l));return a=this,f=arguments,t<=0||e<t?(i&&(clearTimeout(i),i=null),l=n,c=r.apply(a,f),i||(a=f=null)):i||!1===u.trailing||(i=setTimeout(o,t)),c}var i,a,f,c,l=0;return u=u||{},n.cancel=function(){clearTimeout(i),l=0,i=a=f=null},n},debounce:function(t,r,e){function u(){var n=O()-i;n<r?o=setTimeout(u,r-n):(o=null,e||(f=t.apply(c,a)),o||(a=c=null))}var o,i,a,f,c,n=l(function(n){return c=this,a=n,i=O(),o||(o=setTimeout(u,r),e&&(f=t.apply(c,a))),f});return n.cancel=function(){clearTimeout(o),o=a=c=null},n},wrap:function(n,t){return M(t,n)},negate:Yn,compose:function(){var r=arguments,e=r.length-1;return function(){for(var n=e,t=r[e].apply(this,arguments);n--;)t=r[n].call(this,t);return t}},after:function(n,t){return function(){if(--n<1)return t.apply(this,arguments)}},before:Zn,once:nt,findKey:tt,findIndex:et,findLastIndex:ut,sortedIndex:ot,indexOf:at,lastIndexOf:ft,find:ct,detect:ct,findWhere:function(n,t){return ct(n,A(t))},each:N,forEach:N,map:I,collect:I,reduce:st,foldl:st,inject:st,reduceRight:pt,foldr:pt,filter:T,select:T,reject:function(n,t,r){return T(n,Yn(S(t)),r)},every:ht,all:ht,some:vt,any:vt,contains:k,includes:k,include:k,invoke:yt,pluck:dt,where:function(n,t){return T(n,A(t))},max:gt,min:function(n,e,t){var r,u,o=1/0,i=1/0;if(null==e||"number"==typeof e&&"object"!=typeof n[0]&&null!=n)for(var a=0,f=(n=E(n)?n:j(n)).length;a<f;a++)null!=(r=n[a])&&r<o&&(o=r);else e=S(e,t),N(n,function(n,t,r){((u=e(n,t,r))<i||u===1/0&&o===1/0)&&(o=n,i=u)});return o},shuffle:function(n){return jt(n,1/0)},sample:jt,sortBy:function(n,e,t){var u=0;return e=S(e,t),dt(I(n,function(n,t,r){return{value:n,index:u++,criteria:e(n,t,r)}}).sort(function(n,t){var r=n.criteria,e=t.criteria;if(r!==e){if(e<r||void 0===r)return 1;if(r<e||void 0===e)return-1}return n.index-t.index}),"value")},groupBy:wt,indexBy:_t,countBy:At,partition:xt,toArray:mt,size:function(n){return null==n?0:(E(n)?n:b(n)).length},pick:Ot,omit:Mt,first:Bt,head:Bt,take:Bt,initial:Et,last:function(n,t,r){return null==n||n.length<1?null==t||r?void 0:[]:null==t||r?n[n.length-1]:R(n,Math.max(0,n.length-t))},rest:R,tail:R,drop:R,compact:function(n){return T(n,Boolean)},flatten:function(n,t){return B(n,t,!1)},without:It,uniq:Tt,unique:Tt,union:kt,intersection:function(n){for(var t=[],r=arguments.length,e=0,u=g(n);e<u;e++){var o=n[e];if(!k(t,o)){for(var i=1;i<r&&k(arguments[i],o);i++);i===r&&t.push(o)}}return t},difference:Nt,unzip:Dt,transpose:Dt,zip:Rt,object:function(n,t){for(var r={},e=0,u=g(n);e<u;e++)t?r[n[e]]=t[e]:r[n[e][0]]=n[e][1];return r},range:function(n,t,r){null==t&&(t=n||0,n=0),r=r||(t<n?-1:1);for(var e=Math.max(Math.ceil((t-n)/r),0),u=Array(e),o=0;o<e;o++,n+=r)u[o]=n;return u},chunk:function(n,t){if(null==t||t<1)return[];for(var r=[],e=0,u=n.length;e<u;)r.push(f.call(n,e,e+=t));return r},mixin:Ft,default:m});return n._=n});