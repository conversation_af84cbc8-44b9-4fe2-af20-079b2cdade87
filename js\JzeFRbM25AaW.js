(function(e){e.fn.tableHeadFixer=function(t){return this.each(function(){n.call(this)});function n(){{var a={head:!0,foot:!1,left:0,right:0,"z-index":0},n=e.extend({},a,t);n.table=this,n.parent=e(n.table).parent(),c(),n.head==!0&&l(),n.foot==!0&&d(),n.left>0&&u(),n.right>0&&h(),r(),e(n.parent).trigger("scroll"),e(window).resize(function(){e(n.parent).trigger("scroll")});function r(){var t,s=e(n.table);n.head&&(n.left>0&&(t=s.find("thead tr"),t.each(function(t,s){o(s,function(t){e(t).css("z-index",n["z-index"]+1)})})),n.right>0&&(t=s.find("thead tr"),t.each(function(t,s){i(s,function(t){e(t).css("z-index",n["z-index"]+1)})}))),n.foot&&(n.left>0&&(t=s.find("tfoot tr"),t.each(function(t,s){o(s,function(t){e(t).css("z-index",n["z-index"])})})),n.right>0&&(t=s.find("tfoot tr"),t.each(function(t,s){i(s,function(t){e(t).css("z-index",n["z-index"])})})))}function c(){var t=e(n.parent),s=e(n.table);t.append(s),t.css({"overflow-x":"auto","overflow-y":"auto"}),t.scroll(function(){var o=t[0].scrollWidth,i=t[0].clientWidth,a=t[0].scrollHeight,r=t[0].clientHeight,e=t.scrollTop(),s=t.scrollLeft();n.head&&this.find("thead tr > *").css("top",e),n.foot&&this.find("tfoot tr > *").css("bottom",a-r-e),n.left>0&&n.leftColumns.css("left",s),n.right>0&&n.rightColumns.css("right",o-i-s)}.bind(s))}function l(){var t=e(n.table).find("thead"),i=t.find("tr"),o=t.find("tr > *");s(o),o.css({position:"relative"})}function d(){var t=e(n.table).find("tfoot"),i=t.find("tr"),o=t.find("tr > *");s(o),o.css({position:"relative"})}function u(){var t,i,a=e(n.table);n.leftColumns=e(),t=a.find("tr"),t.each(function(e,t){o(t,function(e){n.leftColumns=n.leftColumns.add(e)})}),i=n.leftColumns,i.each(function(t,n){var n=e(n);s(n),n.css({position:"relative"})})}function h(){var t,o,a=e(n.table),r=n.right;n.rightColumns=e(),t=a.find("tr"),t.each(function(e,t){i(t,function(e){n.rightColumns=n.rightColumns.add(e)})}),o=n.rightColumns,o.each(function(t,n){var n=e(n);s(n),n.css({position:"relative"})})}function s(t){return!1;t.each(function(t,n){var n=e(n),a=e(n).parent(),s=n.css("background-color"),s=s=="transparent"||s=="rgba(0, 0, 0, 0)"?null:s,o=a.css("background-color"),o=o=="transparent"||o=="rgba(0, 0, 0, 0)"?null:o,i=o||"white",i=s||i;n.css("background-color",i)})}function o(t,s){for(var i,c,l,r=n.left,a=1,o=1;o<=r;o=o+a)c=a>1?o-1:o,i=e(t).find("> *:nth-child("+c+")"),l=i.prop("colspan"),i.cellPos().left<r&&s(i),a=l}function i(t,s){for(var a,r,c,l=n.right,i=1,o=1;o<=l;o=o+i)r=i>1?o-1:o,a=e(t).find("> *:nth-last-child("+r+")"),c=a.prop("colspan"),s(a),i=c}}}}})(jQuery),function(e){function t(t){var n=[];t.children("tr").each(function(t,s){e(s).children("td, th").each(function(s,o){for(var i,c,d,l=e(o),a=l.attr("colspan")|0,r=l.attr("rowspan")|0,a=a||1,r=r||1;n[t]&&n[t][s];++s);for(c=s;c<s+a;++c)for(i=t;i<t+r;++i)n[i]||(n[i]=[]),n[i][c]=!0;d={top:t,left:s},l.data("cellPos",d)})})}e.fn.cellPos=function(e){var o,n=this.first(),s=n.data("cellPos");return(!s||e)&&(o=n.closest("table, thead, tbody, tfoot"),t(o)),s=n.data("cellPos"),s}}(jQuery)