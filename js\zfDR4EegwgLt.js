(()=>{"use strict";const r=window.wp.i18n,s=e=>Math.abs(parseInt(e,10)),t=(e,t,n)=>{const s=new CustomEvent(`wpcf7${t}`,{bubbles:!0,detail:n});"string"==typeof e&&(e=document.querySelector(e)),e.dispatchEvent(s)},e=(e,n)=>{const o=new Map([["init","init"],["validation_failed","invalid"],["acceptance_missing","unaccepted"],["spam","spam"],["aborted","aborted"],["mail_sent","sent"],["mail_failed","failed"],["submitting","submitting"],["resetting","resetting"],["validating","validating"],["payment_required","payment-required"]]);o.has(n)&&(n=o.get(n)),Array.from(o.values()).includes(n)||(n=`custom-${n=(n=n.replace(/[^0-9a-z]+/i," ").trim()).replace(/\s+/,"-")}`);const s=e.getAttribute("data-status");if(e.wpcf7.status=n,e.setAttribute("data-status",n),e.classList.add(n),s&&s!==n){e.classList.remove(s);const n={contactFormId:e.wpcf7.id,pluginVersion:e.wpcf7.pluginVersion,contactFormLocale:e.wpcf7.locale,unitTag:e.wpcf7.unitTag,containerPostId:e.wpcf7.containerPost,status:e.wpcf7.status,prevStatus:s};t(e,"statuschanged",n)}return n},n=e=>{const{root:t,namespace:n="contact-form-7/v1"}=wpcf7.api;return c.reduceRight((e,t)=>n=>t(n,e),e=>{let i,a,{url:r,path:s,endpoint:c,headers:o,body:l,data:d,...h}=e;"string"==typeof c&&(i=n.replace(/^\/|\/$/g,""),a=c.replace(/^\//,""),s=a?i+"/"+a:i),"string"==typeof s&&(-1!==t.indexOf("?")&&(s=s.replace("?","&")),s=s.replace(/^\//,""),r=t+s),o={Accept:"application/json, */*;q=0.1",...o},delete o["X-WP-Nonce"],d&&(l=JSON.stringify(d),o["Content-Type"]="application/json");const m={code:"fetch_error",message:"You are probably offline."},u={code:"invalid_json",message:"The response is not a valid JSON response."};return window.fetch(r||s||window.location.href,{...h,headers:o,body:l}).then(e=>Promise.resolve(e).then(e=>{if(e.status>=200&&e.status<300)return e;throw e}).then(e=>{if(204===e.status)return null;if(e&&e.json)return e.json().catch(()=>{throw u});throw u}),()=>{throw m})})(e)},c=[];function u(t,n={}){const{target:s,scope:o=t,...u}=n;if(void 0===t.wpcf7?.schema)return;const c={...t.wpcf7.schema};if(void 0!==s){if(!t.contains(s))return;if(!s.closest(".wpcf7-form-control-wrap[data-name]"))return;if(s.closest(".novalidate"))return}const r=o.querySelectorAll(".wpcf7-form-control-wrap"),l=Array.from(r).reduce((e,t)=>(t.closest(".novalidate")||t.querySelectorAll(":where( input, textarea, select ):enabled").forEach(t=>{if(t.name)switch(t.type){case"button":case"image":case"reset":case"submit":break;case"checkbox":case"radio":t.checked&&e.append(t.name,t.value);break;case"select-multiple":for(const n of t.selectedOptions)e.append(t.name,n.value);break;case"file":for(const n of t.files)e.append(t.name,n);break;default:e.append(t.name,t.value)}}),e),new FormData),d=t.getAttribute("data-status");Promise.resolve(e(t,"validating")).then(e=>{if(void 0!==swv){const e=swv.validate(c,l,n);for(const c of r){if(void 0===c.dataset.name)continue;const n=c.dataset.name;if(e.has(n)){const{error:s,validInputs:r}=e.get(n);i(t,n),void 0!==s&&a(t,n,s,{scope:o}),h(t,n,r??[])}if(c.contains(s))break}}}).finally(()=>{e(t,d)})}n.use=e=>{c.unshift(e)};const a=(e,t,n,s)=>{const{scope:a=e,...r}=s??{},i=`${e.wpcf7?.unitTag}-ve-${t}`.replaceAll(/[^0-9a-z_-]+/gi,""),o=e.querySelector(`.wpcf7-form-control-wrap[data-name="${t}"] .wpcf7-form-control`);(()=>{const t=document.createElement("li");t.setAttribute("id",i),o&&o.id?t.insertAdjacentHTML("beforeend",`<a href="#${o.id}">${n}</a>`):t.insertAdjacentText("beforeend",n),e.wpcf7.parent.querySelector(".screen-reader-response ul").appendChild(t)})(),a.querySelectorAll(`.wpcf7-form-control-wrap[data-name="${t}"]`).forEach(e=>{const t=document.createElement("span");t.classList.add("wpcf7-not-valid-tip"),t.setAttribute("aria-hidden","true"),t.insertAdjacentText("beforeend",n),e.appendChild(t),e.querySelectorAll("[aria-invalid]").forEach(e=>{e.setAttribute("aria-invalid","true")}),e.querySelectorAll(".wpcf7-form-control").forEach(e=>{e.classList.add("wpcf7-not-valid"),e.setAttribute("aria-describedby",i),"function"==typeof e.setCustomValidity&&e.setCustomValidity(n),e.closest(".use-floating-validation-tip")&&(e.addEventListener("focus",e=>{t.setAttribute("style","display: none")}),t.addEventListener("click",e=>{t.setAttribute("style","display: none")}))})})},i=(e,t)=>{const n=`${e.wpcf7?.unitTag}-ve-${t}`.replaceAll(/[^0-9a-z_-]+/gi,"");e.wpcf7.parent.querySelector(`.screen-reader-response ul li#${n}`)?.remove(),e.querySelectorAll(`.wpcf7-form-control-wrap[data-name="${t}"]`).forEach(e=>{e.querySelector(".wpcf7-not-valid-tip")?.remove(),e.querySelectorAll("[aria-invalid]").forEach(e=>{e.setAttribute("aria-invalid","false")}),e.querySelectorAll(".wpcf7-form-control").forEach(e=>{e.removeAttribute("aria-describedby"),e.classList.remove("wpcf7-not-valid"),"function"==typeof e.setCustomValidity&&e.setCustomValidity("")})})},h=(e,t,n)=>{e.querySelectorAll(`[data-reflection-of="${t}"]`).forEach(e=>{if("output"===e.tagName.toLowerCase()){const t=e;0===n.length&&n.push(t.dataset.default),n.slice(0,1).forEach(e=>{e instanceof File&&(e=e.name),t.textContent=e})}else e.querySelectorAll("output").forEach(e=>{e.hasAttribute("data-default")?0===n.length?e.removeAttribute("hidden"):e.setAttribute("hidden","hidden"):e.remove()}),n.forEach(n=>{n instanceof File&&(n=n.name);const s=document.createElement("output");s.setAttribute("name",t),s.textContent=n,e.appendChild(s)})})};function m(s,i={}){if(wpcf7.blocked)return o(s),void e(s,"submitting");const c=new FormData(s);i.submitter&&i.submitter.name&&c.append(i.submitter.name,i.submitter.value);const r={contactFormId:s.wpcf7.id,pluginVersion:s.wpcf7.pluginVersion,contactFormLocale:s.wpcf7.locale,unitTag:s.wpcf7.unitTag,containerPostId:s.wpcf7.containerPost,status:s.wpcf7.status,inputs:Array.from(c,e=>{const t=e[0],n=e[1];return!t.match(/^_/)&&{name:t,value:n}}).filter(e=>!1!==e),formData:c};n({endpoint:`contact-forms/${s.wpcf7.id}/feedback`,method:"POST",body:c,wpcf7:{endpoint:"feedback",form:s,detail:r}}).then(n=>{const o=e(s,n.status);return r.status=n.status,r.apiResponse=n,["invalid","unaccepted","spam","aborted"].includes(o)?t(s,o,r):["sent","failed"].includes(o)&&t(s,`mail${o}`,r),t(s,"submit",r),n}).then(e=>{e.posted_data_hash&&(s.querySelector('input[name="_wpcf7_posted_data_hash"]').value=e.posted_data_hash),"mail_sent"===e.status&&(s.reset(),s.wpcf7.resetOnMailSent=!0),e.invalid_fields&&e.invalid_fields.forEach(e=>{a(s,e.field,e.message)}),s.wpcf7.parent.querySelector('.screen-reader-response [role="status"]').insertAdjacentText("beforeend",e.message),s.querySelectorAll(".wpcf7-response-output").forEach(t=>{t.innerText=e.message})}).catch(e=>console.error(e))}n.use((n,s)=>{if(n.wpcf7&&"feedback"===n.wpcf7.endpoint){const{form:s,detail:i}=n.wpcf7;o(s),t(s,"beforesubmit",i),e(s,"submitting")}return s(n)});const o=e=>{e.querySelectorAll(".wpcf7-form-control-wrap").forEach(t=>{t.dataset.name&&i(e,t.dataset.name)}),e.wpcf7.parent.querySelector('.screen-reader-response [role="status"]').innerText="",e.querySelectorAll(".wpcf7-response-output").forEach(e=>{e.innerText=""})};function f(s){const i=new FormData(s),o={contactFormId:s.wpcf7.id,pluginVersion:s.wpcf7.pluginVersion,contactFormLocale:s.wpcf7.locale,unitTag:s.wpcf7.unitTag,containerPostId:s.wpcf7.containerPost,status:s.wpcf7.status,inputs:Array.from(i,e=>{const t=e[0],n=e[1];return!t.match(/^_/)&&{name:t,value:n}}).filter(e=>!1!==e),formData:i};n({endpoint:`contact-forms/${s.wpcf7.id}/refill`,method:"GET",wpcf7:{endpoint:"refill",form:s,detail:o}}).then(n=>{s.wpcf7.resetOnMailSent?(delete s.wpcf7.resetOnMailSent,e(s,"mail_sent")):e(s,"init"),o.apiResponse=n,t(s,"reset",o)}).catch(e=>console.error(e))}n.use((t,n)=>{if(t.wpcf7&&"refill"===t.wpcf7.endpoint){const{form:n,detail:s}=t.wpcf7;o(n),e(n,"resetting")}return n(t)});const l=(e,t)=>{for(const n in t){const s=t[n];e.querySelectorAll(`input[name="${n}"]`).forEach(e=>{e.value=""}),e.querySelectorAll(`img.wpcf7-captcha-${n.replaceAll(":","")}`).forEach(e=>{e.setAttribute("src",s)});const o=/([0-9]+)\.(png|gif|jpeg)$/.exec(s);o&&e.querySelectorAll(`input[name="_wpcf7_captcha_challenge_${n}"]`).forEach(e=>{e.value=o[1]})}},d=(e,t)=>{for(const n in t){const s=t[n][0],o=t[n][1];e.querySelectorAll(`.wpcf7-form-control-wrap[data-name="${n}"]`).forEach(e=>{e.querySelector(`input[name="${n}"]`).value="",e.querySelector(".wpcf7-quiz-label").textContent=s,e.querySelector(`input[name="_wpcf7_quiz_answer_${n}"]`).value=o})}};function p(e){const t=new FormData(e);e.wpcf7={id:s(t.get("_wpcf7")),status:e.getAttribute("data-status"),pluginVersion:t.get("_wpcf7_version"),locale:t.get("_wpcf7_locale"),unitTag:t.get("_wpcf7_unit_tag"),containerPost:s(t.get("_wpcf7_container_post")),parent:e.closest(".wpcf7"),get schema(){return wpcf7.schemas.get(this.id)}},wpcf7.schemas.set(e.wpcf7.id,void 0),e.querySelectorAll(".has-spinner").forEach(e=>{e.insertAdjacentHTML("afterend",'<span class="wpcf7-spinner"></span>')}),(e=>{e.querySelectorAll(".wpcf7-exclusive-checkbox").forEach(t=>{t.addEventListener("change",t=>{const n=t.target.getAttribute("name");e.querySelectorAll(`input[type="checkbox"][name="${n}"]`).forEach(e=>{e!==t.target&&(e.checked=!1)})})})})(e),(e=>{e.querySelectorAll(".has-free-text").forEach(t=>{const s=t.querySelector("input.wpcf7-free-text"),n=t.querySelector('input[type="checkbox"], input[type="radio"]');s.disabled=!n.checked,e.addEventListener("change",e=>{s.disabled=!n.checked,e.target===n&&n.checked&&s.focus()})})})(e),(e=>{e.querySelectorAll(".wpcf7-validates-as-url").forEach(e=>{e.addEventListener("change",t=>{let n=e.value.trim();n&&!n.match(/^[a-z][a-z0-9.+-]*:/i)&&-1!==n.indexOf(".")&&(n=n.replace(/^\/+/,""),n="http://"+n),e.value=n})})})(e),(e=>{if(!e.querySelector(".wpcf7-acceptance")||e.classList.contains("wpcf7-acceptance-as-validation"))return;const t=()=>{let t=!0;e.querySelectorAll(".wpcf7-acceptance").forEach(e=>{if(!t||e.classList.contains("optional"))return;const n=e.querySelector('input[type="checkbox"]');(e.classList.contains("invert")&&n.checked||!e.classList.contains("invert")&&!n.checked)&&(t=!1)}),e.querySelectorAll(".wpcf7-submit").forEach(e=>{e.disabled=!t})};t(),e.addEventListener("change",e=>{t()}),e.addEventListener("wpcf7reset",e=>{t()})})(e),(e=>{const t=(e,t)=>{const a=s(e.getAttribute("data-starting-value")),n=s(e.getAttribute("data-maximum-value")),o=s(e.getAttribute("data-minimum-value")),i=e.classList.contains("down")?a-t.value.trim().length:t.value.trim().length;e.setAttribute("data-current-value",i),e.innerText=i,n&&n<t.value.length?e.classList.add("too-long"):e.classList.remove("too-long"),o&&t.value.length<o?e.classList.add("too-short"):e.classList.remove("too-short")},n=n=>{n={init:!1,...n},e.querySelectorAll(".wpcf7-character-count").forEach(s=>{const i=s.getAttribute("data-target-name"),o=e.querySelector(`[name="${i}"]`);o&&(o.value=o.defaultValue,t(s,o),n.init&&o.addEventListener("keyup",e=>{t(s,o)}))})};n({init:!0}),e.addEventListener("wpcf7reset",e=>{n()})})(e),window.addEventListener("load",t=>{wpcf7.cached&&e.reset()}),e.addEventListener("reset",t=>{wpcf7.reset(e)}),e.addEventListener("submit",t=>{wpcf7.submit(e,{submitter:t.submitter}),t.preventDefault()}),e.addEventListener("wpcf7submit",t=>{t.detail.apiResponse.captcha&&l(e,t.detail.apiResponse.captcha),t.detail.apiResponse.quiz&&d(e,t.detail.apiResponse.quiz)}),e.addEventListener("wpcf7reset",t=>{t.detail.apiResponse.captcha&&l(e,t.detail.apiResponse.captcha),t.detail.apiResponse.quiz&&d(e,t.detail.apiResponse.quiz)}),e.addEventListener("change",t=>{t.target.closest(".wpcf7-form-control")&&wpcf7.validate(e,{target:t.target})}),e.addEventListener("wpcf7statuschanged",t=>{const n=t.detail.status;e.querySelectorAll(".active-on-any").forEach(e=>{e.removeAttribute("inert"),e.classList.remove("active-on-any")}),e.querySelectorAll(`.inert-on-${n}`).forEach(e=>{e.setAttribute("inert","inert"),e.classList.add("active-on-any")})})}document.addEventListener("DOMContentLoaded",e=>{var t;if("undefined"!=typeof wpcf7)if(void 0!==wpcf7.api)if("function"==typeof window.fetch)if("function"==typeof window.FormData)if("function"==typeof NodeList.prototype.forEach)if("function"==typeof String.prototype.replaceAll){wpcf7={init:p,submit:m,reset:f,validate:u,schemas:new Map,...null!==(t=wpcf7)&&void 0!==t?t:{}},document.querySelectorAll("form .wpcf7[data-wpcf7-id]").forEach(e=>{const t=document.createElement("p");t.setAttribute("class","wpcf7-form-in-wrong-place");const n=document.createElement("strong");n.append((0,r.__)("Error:","contact-form-7"));const s=(0,r.__)("This contact form is placed in the wrong place.","contact-form-7");t.append(n," ",s),e.replaceWith(t)}),document.querySelectorAll(".wpcf7 > form").forEach(e=>{wpcf7.init(e),e.closest(".wpcf7").classList.replace("no-js","js")});for(const e of wpcf7.schemas.keys())n({endpoint:`contact-forms/${e}/feedback/schema`,method:"GET"}).then(t=>{wpcf7.schemas.set(e,t)})}else console.error("Your browser does not support String.replaceAll().");else console.error("Your browser does not support NodeList.forEach().");else console.error("Your browser does not support window.FormData().");else console.error("Your browser does not support window.fetch().");else console.error("wpcf7.api is not defined.");else console.error("wpcf7 is not defined.")})})()