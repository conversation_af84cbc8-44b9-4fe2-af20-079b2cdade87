jQuery(document).ready(function(){window.gsap&&window.ScrollTrigger&&gsap.registerPlugin(ScrollTrigger);function t(){const e=document.querySelectorAll('[data-scroll-effect="scroll-item-run"]');e.forEach(e=>{const s=e.getAttribute("data-trigger-top")||"0",n=parseInt(s,10)||0;let t=0;e.classList.contains("scroll-item-run-right-to-left")?t=100:e.classList.contains("scroll-item-run-left-to-right")&&(t=-100),gsap.set(e,{xPercent:t,willChange:"transform"}),gsap.to(e,{xPercent:0,ease:"none",scrollTrigger:{trigger:e,start:`top+=${n} bottom`,end:()=>`top+=${n} ${window.innerHeight*(2/3)}`,scrub:!0,invalidateOnRefresh:!0}})})}document.readyState!=="loading"?t():document.addEventListener("DOMContentLoaded",t)})