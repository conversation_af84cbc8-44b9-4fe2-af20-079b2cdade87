(function(e){"use strict";function t(t,n,s){var o,r,c,u,f,i=e(t),a=i.clone(n,s),m=i.find("textarea").add(i.filter("textarea")),g=a.find("textarea").add(a.filter("textarea")),d=i.find("select").add(i.filter("select")),p=a.find("select").add(a.filter("select")),l=i.find("canvas").add(i.filter("canvas")),h=a.find("canvas").add(a.filter("canvas"));for(o=0,r=m.length;o<r;++o)e(g[o]).val(e(m[o]).val());for(o=0,r=d.length;o<r;++o)for(c=0,f=d[o].options.length;c<f;++c)d[o].options[c].selected===!0&&(p[o].options[c].selected=!0);for(o=0,r=l.length;o<r;++o)u=l[o].getContext("2d"),u&&(h[o].getContext("2d").drawImage(l[o],0,0),e(h[o]).attr("data-jquery-print",u.canvas.toDataURL()));return a}function s(n){var s=e("");try{s=t(n)}catch{s=e("<span />").html(n)}return s}function o(t,n,s){a=e.Deferred();try{t=t.contentWindow||t.contentDocument||t;try{t.resizeTo(window.innerWidth,window.innerHeight)}catch(e){console.warn(e)}o=t.document||t.contentDocument||t,s.doctype&&o.write(s.doctype),o.write(n);try{for(r=o.querySelectorAll("canvas"),i=0;i<r.length;i++)l=r[i].getContext("2d"),c=new Image,c.onload=function(){l.drawImage(c,0,0)},c.src=r[i].getAttribute("data-jquery-print")}catch(e){console.warn(e)}o.close();var o,i,a,r,c,l,d=!1,u=function(){if(d)return;t.focus();try{t.document.execCommand("print",!1,null)||t.print(),e("body").focus()}catch{t.print()}t.close(),d=!0,a.resolve()};e(t).on("load",u),setTimeout(u,s.timeout)}catch(e){a.reject(e)}return a}function a(t,s){var r,i=e(s.iframe+""),a=i.length;return a===0&&(i=e('<iframe height="0" width="0" border="0" wmode="Opaque"/>').prependTo("body").css({position:"absolute",top:-999,left:-999})),r=i.get(0),o(r,t,s).done(function(){setTimeout(function(){a===0&&i.remove()},1e3)}).fail(function(e){console.error("Failed to print from iframe",e),n(t,s)}).always(function(){try{s.deferred.resolve()}catch(e){console.warn("Error notifying deferred",e)}})}function n(e,t){var n=window.open();return o(n,e,t).always(function(){try{t.deferred.resolve()}catch(e){console.warn("Error notifying deferred",e)}})}function i(e){return!!(typeof Node=="object"?e instanceof Node:e&&typeof e=="object"&&typeof e.nodeType=="number"&&typeof e.nodeName=="string")}e.print=e.fn.print=function(){var o,r,c,l,u,h,m,f,d=this;if(d instanceof e&&(d=d.get(0)),i(d)?(c=e(d),arguments.length>0&&(o=arguments[0])):arguments.length>0?(c=e(arguments[0]),i(c[0])?arguments.length>1&&(o=arguments[1]):(o=arguments[0],c=e("html"))):c=e("html"),f={globalStyles:!0,mediaPrint:!1,stylesheet:null,noPrintSelector:".no-print",iframe:!0,append:null,prepend:null,manuallyCopyFormValues:!0,deferred:e.Deferred(),timeout:750,title:null,doctype:"<!doctype html>"},o=e.extend({},f,o||{}),l=e(""),o.globalStyles?l=e("style, link, meta, base, title"):o.mediaPrint&&(l=e("link[media=print]")),o.stylesheet){(e.isArray?e.isArray:Array.isArray)(o.stylesheet)||(o.stylesheet=[o.stylesheet]);for(m=0;m<o.stylesheet.length;m++)l=e.merge(l,e('<link rel="stylesheet" href="'+o.stylesheet[m]+'">'))}r=t(c,!0,!0),r=e("<span/>").append(r),r.find(o.noPrintSelector).remove(),r.append(t(l)),o.title&&(u=e("title",r),u.length===0&&(u=e("<title />"),r.append(u)),u.text(o.title)),r.append(s(o.append)),r.prepend(s(o.prepend)),o.manuallyCopyFormValues&&(r.find("input").each(function(){var t=e(this);t.is("[type='radio']")||t.is("[type='checkbox']")?t.prop("checked")&&t.attr("checked","checked"):t.attr("value",t.val())}),r.find("select").each(function(){var t=e(this);t.find(":selected").attr("selected","selected")}),r.find("textarea").each(function(){var t=e(this);t.text(t.val())})),h=r.html();try{o.deferred.notify("generated_markup",h,r)}catch(e){console.warn("Error notifying deferred",e)}if(r.remove(),o.iframe)try{a(h,o)}catch(e){console.error("Failed to print from iframe",e.stack,e.message),n(h,o)}else n(h,o);return this}})(jQuery)