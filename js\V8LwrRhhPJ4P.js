(function(e){"use strict";var t,n,s,o="",a=0,O=!1;e(window).on("load",function(){let t=e(".preloader");t.length&&(t.hasClass("loader-style3")?setTimeout(function(){e(".preloader").addClass("loaded").removeClass("loading")},3e3):e(".preloader").addClass("loaded").removeClass("loading")),e(".pxl-loader").addClass("is-loaded"),e(".pxl-swiper-slider, .pxl-header-mobile-elementor").css("opacity","1"),e(".pxl-gallery-scroll").parents("body").addClass("body-overflow").addClass("body-visible-sm"),s=e(window).width(),n=e(window).height(),i(),l(),d(),u(),h(),p(),r(),c(),m()}),e(window).on("scroll",function(){t=e(window).scrollTop(),n=e(window).height(),s=e(window).width(),t<a?o="up":o="down",a=t,i(),d(),u(),f(),c(),t<100&&e(".elementor > .pin-spacer").removeClass("scroll-top-active")}),e(window).on("resize",function(){n=e(window).height(),s=e(window).width(),h(),l(),r(),setTimeout(function(){m()},500)}),e(document).ready(function(){e(".preloader").addClass("loading"),g(),v(),b(),j(),setInterval(_,200),e(".pxl-marquee").each(function(){var o,i,a,r,n=e(this),t=n.find("ul"),s=n.find("li.pxl-marquee__item");if(s.length===0)return;for(o=0;o<s.length;o++)s.eq(o).clone(!0).appendTo(t);n.hasClass("pxl-marquee__pause-on-hover")&&t.on("mouseenter",function(){e(this).css("animation-play-state","paused")}).on("mouseleave",function(){e(this).css("animation-play-state","running")}),i=n.find("li.pxl-marquee__item").length,a=parseFloat(t.css("animation-duration"))||function(){var n,e=t.attr("style");return e&&e.indexOf("animation-duration")!==-1?(n=e.match(/animation-duration:\s*([0-9.]+)s/),n?parseFloat(n[1]):10):10}(),r=a*i/s.length,t.css("animation-duration",r+"s")}),e(document).on("click",".pxl-copy__button",function(){var s,a,t=e(this).closest(".pxl-copy"),o=t.find(".pxl-copy__text").text().trim(),i=t.data("success")||"Copied!",n=e("<textarea>");e("body").append(n),n.val(o).select(),document.execCommand("copy"),n.remove(),a=e(this).html(),e(this).css("pointer-events","none"),t.find(".pxl-copy__notify").remove(),e(this).parent().append('<span class="pxl-copy__notify">'+i+"</span>"),s=e(this),setTimeout(function(){t.find(".pxl-copy__notify").remove(),s.css("pointer-events","auto")},2e3)});let d=e(".pxl-step__style-2");d.length&&d.each(function(t,n){const o=e(n),d=o.find(".pxl-step__feature-line"),s=o.find(".pxl-step__feature-item"),a=o.hasClass("auto-play");let i=null,r=!1,c=!1;function u(t){let n=t.index();const i=t.closest(".e-con-inner").find(".pxl-step-image__list li");i.removeClass("active"),n>=0&&n<i.length&&i.eq(n).addClass("active");const a=s.length-1;n>a&&(n=a),s.removeClass("active"),s.each(function(t){t<=n&&e(this).addClass("active")});const r=s.eq(n),l=r.position().top||0,u=r.outerHeight()||0,h=o.outerHeight()||1,m=l+u/2,c=(.3+m/h)*100;c>=95?d.css({height:"100%",background:"#2E90FA"}):d.css({height:c+"%",background:"linear-gradient(180deg, #2E90FA 0%, #252B37 100%)"})}function m(){const t=s.filter(".active"),n=t.length?t.last().index():-1;let e=-1;for(let t=n+1;t<s.length;t++)if(!s.eq(t).hasClass("active")){e=t;break}e===-1&&(s.removeClass("active"),e=0),u(s.eq(e))}function l(){if(i)return;i=setInterval(m,3e3)}function h(){i&&(clearInterval(i),i=null)}function f(){if(!a)return;r=!0,h(),setTimeout(function(){r=!1,l()},3e3)}s.off("click.pxlStep").on("click.pxlStep",function(){u(e(this)),f()}),o.on("pointerenter",function(){c=!0}),o.on("mouseenter",function(){a&&c&&!r&&h()}),o.on("mouseleave",function(){a&&c&&!r&&l()}),a&&l()}),e(document).on("click",".pxl-pricing__style-4 .pxl-pricing__option",function(){const n=e(this),t=n.prev(),s=n.hasClass("active"),o=s?n.data("st"):n.data("nd"),i=s?t.find(".pxl-pricing__price-detail span").data("price-st"):t.find(".pxl-pricing__price-detail span").data("price-nd"),a=s?t.find(".pxl-pricing__price-period").data("period-st"):t.find(".pxl-pricing__price-period").data("period-nd");n.toggleClass("active").find(".pxl-pricing__option-text").text(o),t.find(".pxl-pricing__price-detail span").text(i),t.find(".pxl-pricing__price-period").text(a)}),e(document).on("click",".pxl-pricing__style-5 .pxl-pricing__option",function(){const n=e(this),t=n.closest(".pxl-pricing"),s=n.hasClass("active"),o=s?n.find(".pxl-pricing__option-text").data("tb-st"):n.find(".pxl-pricing__option-text").data("tb-nd"),i=s?t.find(".pxl-pricing__price-currency").data("price-st"):t.find(".pxl-pricing__price-currency").data("price-nd"),a=s?t.find(".pxl-pricing__price-sale").data("price-sale-st"):t.find(".pxl-pricing__price-sale").data("price-sale-nd"),r=s?t.find(".pxl-pricing__price-period").data("period-st"):t.find(".pxl-pricing__price-period").data("period-nd");n.toggleClass("active").find(".pxl-pricing__option-text").text(o),t.find(".pxl-pricing__price-currency").text(`$${i}`),t.find(".pxl-pricing__price-sale").text(`$${a}`),t.find(".pxl-pricing__price-period").text(r)});let n=document.querySelector("#your-phone");if(n){let e=window.intlTelInput(n,{initialCountry:"us",preferredCountries:["us"],utilsScript:"./libs/intlTelInput-utils.min.js"});const t={us:"+****************",ca:"+****************",mx:"+52 55 5123 4567",gt:"+502 2123 4567",bz:"+************",sv:"+503 2123 4567",hn:"+504 9123 4567",ni:"+505 8123 4567",cr:"+506 8123 4567",pa:"+507 6123 4567",cu:"+53 5123 4567",jm:"****** 123 4567",ht:"+509 3123 4567",do:"****** 123 4567",pr:"****** 123 4567",bs:"****** 123 4567",bb:"****** 123 4567",tt:"****** 123 4567",dm:"****** 123 4567",lc:"****** 123 4567",vc:"****** 123 4567",gd:"****** 123 4567",kn:"****** 123 4567",ag:"****** 123 4567",ai:"****** 123 4567",ms:"****** 123 4567",vg:"****** 123 4567",vi:"****** 123 4567",ky:"****** 123 4567",tc:"****** 123 4567",bm:"****** 123 4567",sx:"****** 123 4567",br:"+55 11 91234-5678",ar:"+54 9 11 1234-5678",co:"+57 ************",pe:"+51 987 654 321",cl:"+56 9 1234 5678",ve:"+58 ************",ec:"+593 99 123 4567",bo:"+591 71234567",py:"+595 971 234567",uy:"+598 94 123 456",gy:"+************",sr:"+************",gf:"+594 694 12 34 56",fk:"+500 12345",gb:"+44 7700 900123",fr:"+33 6 12 34 56 78",de:"+49 170 1234567",it:"+39 320 123 4567",es:"+34 612 345 678",pt:"+351 912 345 678",ie:"+353 85 123 4567",be:"+32 470 12 34 56",nl:"+31 6 12345678",lu:"+352 621 123 456",ch:"+41 78 123 45 67",at:"+43 664 123456",mc:"+377 6 12 34 56 78",li:"+423 661 12 34",sm:"+378 66 12 34 56",va:"+39 06 698 12345",se:"+46 70-123 45 67",no:"+47 400 12 345",fi:"+358 40 1234567",ax:"+358 18 12345",dk:"+45 20 12 34 56",is:"+354 661 2345",fo:"+298 212 345",sj:"+47 79 12 34 56",pl:"+48 512 345 678",cz:"+420 601 123 456",sk:"+421 905 123 456",hu:"+36 20 123 4567",ro:"+40 712 345 678",bg:"+359 87 123 4567",ua:"+380 50 123 4567",by:"+375 29 123 45 67",ru:"+7 912 345-67-89",md:"+373 62 123456",ee:"+372 5123 456",lv:"+371 21 234 567",lt:"+370 6 123 4567",hr:"+385 91 234 5678",si:"+386 30 123 456",ba:"+387 61 123 456",rs:"+381 60 1234567",me:"+382 67 123 456",mk:"+389 70 123 456",al:"+355 69 123 4567",xk:"+383 44 123 456",gr:"+30 690 123 4567",cy:"+357 96 123 456",mt:"+356 9612 3456",gi:"+350 57123456",tr:"+90 530 123 45 67",il:"+972 50-123-4567",sa:"+966 50 123 4567",ae:"+971 50 123 4567",qa:"+974 3312 3456",bh:"+973 3312 3456",kw:"+965 500 12345",om:"+968 9123 4567",jo:"+962 7 9012 3456",lb:"+961 71 123 456",sy:"+963 944 123 456",iq:"+964 750 123 4567",ir:"+98 912 345 6789",ye:"+967 71 123 4567",ps:"+970 59 123 4567",kz:"+7 701 123 4567",uz:"+998 90 123 45 67",tm:"+993 65 123456",tj:"+992 917 123 456",kg:"+996 700 123 456",in:"+91 98765 43210",pk:"+92 301 2345678",bd:"+880 1712 345678",np:"+977 984 1234567",lk:"+94 71 234 5678",mv:"+960 771 2345",bt:"+975 17 123 456",af:"+93 70 123 4567",cn:"+86 131 2345 6789",jp:"+81 90-1234-5678",kr:"+82 10-1234-5678",kp:"+850 192 123 4567",tw:"+886 912 345 678",hk:"+852 9123 4567",mo:"+853 6123 4567",mn:"+976 8812 3456",th:"+66 8 1234 5678",vn:"+84 91 234 56 78",id:"+62 812 345 6789",my:"+60 12 345 6789",sg:"+65 8123 4567",ph:"+63 917 123 4567",mm:"+95 9 123 456 789",kh:"+855 12 345 678",la:"+856 20 2345 6789",bn:"+673 712 3456",tl:"+670 7723 4567",au:"+61 412 345 678",nz:"+64 21 123 4567",pg:"+675 7012 3456",fj:"+679 701 2345",sb:"+677 74 12345",vu:"+678 5912 345",ws:"+685 72 12345",to:"+676 7712 345",nr:"+674 555 1234",ki:"+686 72 12345",fm:"+691 350 1234",mh:"+692 235 1234",pw:"+680 779 1234",ck:"+682 55 123",nu:"+683 4123",tk:"+690 7012",nf:"+672 3 12345",nc:"+687 75 12 34",pf:"+689 87 12 34 56",wf:"+681 50 12 34",as:"+1 684 733 1234",gu:"+1 671 300 1234",mp:"+1 670 234 5678",cx:"+61 8 9164 1234",cc:"+61 8 9162 1234",eg:"+20 100 123 4567",ly:"+218 91 2345678",tn:"+216 20 123 456",dz:"+213 551 23 45 67",ma:"+212 612 345678",eh:"+212 5288 12345",ng:"+234 701 234 5678",gh:"+233 24 123 4567",ci:"+225 01 23 45 67 89",sn:"+221 70 123 45 67",gm:"+220 301 2345",gn:"+224 601 12 34 56",ml:"+223 65 01 23 45",bf:"+226 70 12 34 56",ne:"+227 90 12 34 56",tg:"+228 90 12 34 56",bj:"+229 90 12 34 56",lr:"+231 77 012 3456",sl:"+232 76 123456",gw:"+245 955 012 345",cv:"+238 991 2345",cm:"+237 6 71 23 45 67",ga:"+241 6 12 34 56",cg:"+242 06 123 4567",cd:"+243 991 234 567",cf:"+236 70 01 23 45",td:"+235 60 12 34 56",gq:"+240 222 123 456",st:"+239 990 1234",ke:"+254 712 345678",et:"+251 91 234 5678",tz:"+255 712 345678",ug:"+256 712 345678",rw:"+250 722 123 456",bi:"+257 79 123 456",so:"+252 612 345678",dj:"+253 77 83 12 34",er:"+291 7 123 456",ss:"+211 977 123 456",sd:"+249 91 123 4567",za:"+27 71 123 4567",na:"+264 81 123 4567",bw:"+267 71 123 456",zw:"+263 71 234 5678",mz:"+258 82 123 4567",mg:"+261 32 12 345 67",mu:"+230 5 123 4567",zm:"+260 955 123456",ls:"+266 5012 3456",sz:"+268 7612 3456",km:"+269 321 23 45",sc:"+248 2 510 123",io:"+************",sh:"+290 12345",ac:"+247 12345",pm:"+508 55 12 34",bl:"+590 690 12 34 56",mf:"+590 690 12 34 56",re:"+262 692 12 34 56",yt:"+262 639 12 34 56",mq:"+596 696 12 34 56",gp:"+590 690 12 34 56",gg:"+44 7781 123456",je:"+44 7797 123456",im:"+44 7624 123456"};e.setCountry("us"),n.addEventListener("countrychange",function(){const t=e.getSelectedCountryData().iso2;m(t)});function m(e){const n=document.querySelector("#your-phone");n&&t[e]&&n.setAttribute("placeholder",t[e])}}let r=document.querySelectorAll(".pxl-office");r.length>0&&r.forEach(function(t){e(t).find(".pxl-office__pos-item").on("click",function(){let n=e(this).index();e(t).find(".pxl-office__pos-item").removeClass("active"),e(t).find(".pxl-office__content-item").removeClass("active"),e(this).addClass("active"),e(t).find(".pxl-office__content-item").eq(n).addClass("active")})});let i=parseInt(loadmore_params.startPage)+1,a=!1;e(".pxl-posts__loadmore").on("click",function(t){if(t.preventDefault(),a)return;a=!0;var n=e(this);n.addClass("loading"),e.ajax({type:"POST",url:loadmore_params.ajax_url,data:{action:"load_more_posts",page:i},success:function(t){t.success&&t.data.trim()!==""&&(i++,w(i,n,function(){e("#pxl-content-main").append(t.data),a=!1,n.removeClass("loading")}))}})}),e(".pxl-banner-box__style-3, .pxl-banner-box__style-4, .pxl-banner-box__style-5").each(function(){let s=e(this),t=s.find(".pxl-banner-box__feature-item"),n=0;if(t.length===0)return;t.removeClass("active").first().addClass("active");let o=t.length;s.hasClass("pxl-banner-box__style-5")&&(o=t.length+1),setInterval(function(){t.removeClass("active"),n<t.length&&t.eq(n).addClass("active"),n=(n+1)%o},2e3)}),e(".pxl-modal-close").on("click",function(){e(this).parent().removeClass("open").addClass("remove"),e(this).parents("body").removeClass("ov-hidden")}),e(".btn-sign-up").on("click",function(){e(".pxl-user-register").addClass("u-open").removeClass("u-close"),e(".pxl-user-login").addClass("u-close").removeClass("u-open")}),e(".btn-sign-in").on("click",function(){e(".pxl-user-register").addClass("u-close").removeClass("u-open"),e(".pxl-user-login").addClass("u-open").removeClass("u-close")}),e(".pxl-user-have-an-account").on("click",function(){e(this).parents(".pxl-modal-content").find(".pxl-user-register").addClass("u-close").removeClass("u-open"),e(this).parents(".pxl-modal-content").find(".pxl-user-login").addClass("u-open").removeClass("u-close")}),e(".btn-user").on("click",function(){const n=e(this).attr("href");(!n||n!=="#")&&(e(".pxl-user-popup").addClass("open").removeClass("remove"),e(this).find(".pxl-user-account").toggleClass("active"))}),e(".pxl-header-menu li.menu-item-has-children").append('<span class="pxl-menu-toggle"></span>'),e(".pxl-menu-toggle").on("click",function(){e(this).hasClass("active")?(e(this).closest("ul").find(".pxl-menu-toggle.active").toggleClass("active"),e(this).closest("ul").find(".sub-menu.active").toggleClass("active").slideToggle()):(e(this).closest("ul").find(".pxl-menu-toggle.active").toggleClass("active"),e(this).closest("ul").find(".sub-menu.active").toggleClass("active").slideToggle(),e(this).toggleClass("active"),e(this).parent().find("> .sub-menu").toggleClass("active"),e(this).parent().find("> .sub-menu").slideToggle())}),e(".pxl-nav").each(function(){e(this).closest(".e-con-inner").find(".pxl-swiper__nav").hide(),e(this).closest(".e-con-inner").find(".pxl-nav__item").on("click",function(){e(this).hasClass("pxl-nav__prev")&&e(this).closest(".e-con-inner").find(".pxl-swiper__nav-prev").trigger("click"),e(this).hasClass("pxl-nav__next")&&e(this).closest(".e-con-inner").find(".pxl-swiper__nav-next").trigger("click")})}),e("#pxl-nav-mobile, .pxl-anchor-mobile-menu").on("click",function(){e(this).toggleClass("active"),e("body").toggleClass("body-overflow"),e(".pxl-header-menu").toggleClass("active")}),e(".pxl-menu-close, .pxl-header-menu-backdrop, #pxl-header-mobile .pxl-menu__primary a.is-one-page").on("click",function(){e(this).parents(".pxl-header-main").find(".pxl-header-menu").removeClass("active"),e("#pxl-nav-mobile").removeClass("active"),e("body").toggleClass("body-overflow")}),e(".pxl-nav-vertical li.menu-item-has-children > a").append('<span class="pxl-arrow-toggle"><i class="caseicon-right-arrow"></i></span>'),e(".pxl-nav-vertical li.menu-item-has-children > a").on("click",function(){e(this).hasClass("active")?e(this).next().toggleClass("active").slideToggle():(e(this).closest("ul").find(".sub-menu.active").toggleClass("active").slideToggle(),e(this).closest("ul").find("a.active").toggleClass("active"),e(this).find(".pxl-menu-toggle.active").toggleClass("active"),e(this).toggleClass("active"),e(this).next().toggleClass("active").slideToggle())}),e(".pxl-menu-hidden-sidebar li.menu-item-has-children > a").append('<span class="pxl-arrow-toggle"><i class="caseicon-right-arrow"></i></span>'),e(".pxl-menu-hidden-sidebar li.menu-item-has-children > a").on("click",function(){e(this).hasClass("active")?e(this).next().toggleClass("active").slideToggle():(e(this).closest("ul").find(".sub-menu.active").toggleClass("active").slideToggle(),e(this).closest("ul").find("a.active").toggleClass("active"),e(this).find(".pxl-menu-toggle.active").toggleClass("active"),e(this).toggleClass("active"),e(this).next().toggleClass("active").slideToggle())}),e(".pxl-menu-hidden-sidebar .pxl-menu-button").on("click",function(){e(this).parents(".pxl-menu-hidden-sidebar").toggleClass("active"),e(this).parents(".pxl-menu-hidden-sidebar").removeClass("boxOut"),e(this).parents("body").toggleClass("body-overflow")}),e(".pxl-menu-popup-overlay").on("click",function(){e(this).parent().removeClass("active"),e(this).parent().addClass("boxOut"),e(this).parents("body").removeClass("body-overflow")}),e(".pxl-menu-popup-close, .pxl-menu-hidden-sidebar .pxl-menu-hidden a.is-one-page").on("click",function(){e(this).parents(".pxl-menu-hidden-sidebar").removeClass("active"),e(this).parents(".pxl-menu-hidden-sidebar").addClass("boxOut"),e(this).parents("body").removeClass("body-overflow")}),navigator.userAgent.includes("Safari")&&!navigator.userAgent.includes("Chrome")&&!navigator.userAgent.includes("Edg")&&(e(".pxl-marquee--coin").addClass("safari"),setTimeout(()=>{e(".pxl-marquee--coin").addClass("safari")},1e3));var l,h=e("li.pxl-megamenu > .sub-menu > .pxl-mega-menu-elementor").outerHeight(),c=e(window).height(),u=c-120;h>c&&(e("li.pxl-megamenu > .sub-menu > .pxl-mega-menu-elementor").css({"max-height":u+"px","overflow-x":"hidden","overflow-y":"auto"}),e("li.pxl-megamenu > .sub-menu > .pxl-mega-menu-elementor").css("overflow-x","hidden")),e(".pxl-chart-week").each(function(){let e=this;ScrollTrigger.create({trigger:e,start:"top bottom",end:"bottom top",toggleClass:{targets:e,className:"active"},onEnter:()=>e.classList.add("active"),onEnterBack:()=>e.classList.add("active")})});let t=!1;e(document).on("click",".pxl-scroll-top",function(){return e("html").hasClass("html-smooth-scroll")?setTimeout(()=>{e("html").css("scroll-behavior","smooth"),t=!0,window.scrollTo({top:0,behavior:"smooth"})},1e3):window.scrollTo({top:0,behavior:"smooth"}),!1}),window.addEventListener("scroll",function(){t&&window.scrollY===0&&(e("html").css("scroll-behavior","auto"),t=!1)}),e(".pxl-grid__masonry").each(function(){var t=80,s=e(this).children().length,n=s-1;e(this).find("> .pxl-grid__item > .wow").each(function(o){e(this).css("animation-delay",t+"ms"),n===o?(t=80,n=n+s):t=t+80})}),e(".btn-text-nina").each(function(){var t=.045,n=e(this).children().length,s=n-1;e(this).find("> .pxl--btn-text > span").each(function(){e(this).css("transition-delay",t+"s"),t=t+.045})}),e(".btn-text-nanuk").each(function(){var t=.05,n=e(this).children().length,s=n-1;e(this).find("> .pxl--btn-text > span").each(function(){e(this).css("animation-delay",t+"s"),t=t+.05})}),e(".btn-text-smoke").each(function(){var t=.05,n=e(this).children().length,s=n-1;e(this).find("> .pxl--btn-text > span > span > span").each(function(){e(this).css("--d",t+"s"),t=t+.05})}),e(".btn-text-reverse .pxl-text--front, .btn-text-reverse .pxl-text--back").each(function(){var t=.05,n=e(this).children().length,s=n-1;e(this).find(".pxl-text--inner > span").each(function(){e(this).css("transition-delay",t+"s"),t=t+.05})}),setTimeout(function(){e(".pxl-video__popup").magnificPopup({type:"iframe",mainClass:"mfp-fade",removalDelay:160,preloader:!1,fixedContentPos:!1})},300),e(".pxl-gallery-lightbox").each(function(){e(this).magnificPopup({delegate:"a.lightbox",type:"image",gallery:{enabled:!0},mainClass:"mfp-fade"})}),l=e(".el-bounce, .pxl-image-effect1, .el-effect-zigzag"),e.each(l,function(e,t){o(t,"active")});function o(t,n){t=e(t);let i=0;t.hasClass(n)?s(function(){return i++,i==2},function(){i=0,t.removeClass(n),o(t,n)},"Deactivate",1e3):s(function(){return i++,i==3},function(){i=0,t.addClass(n),o(t,n)},"Activate",1e3)}function s(e,t,n,o){(n==null||n==""||typeof n=="undefined")&&(n="Timeout"),(o==null||o==""||typeof o=="undefined")&&(o=100);var i=e();i?t():setTimeout(function(){s(e,t,n,o)},o)}e(".pxl-image__tilt").length&&(e(".pxl-image__tilt").parents(".e-con-inner").addClass("pxl-image__tilt-active"),e(".pxl-image__tilt").each(function(){var t=e(this).data("maxtilt"),n=e(this).data("speedtilt"),s=e(this).data("perspectivetilt");VanillaTilt.init(this,{max:t,speed:n,perspective:s})})),e(".pxl-image__3d-ui").each(function(){const n=e(this).find("img")[0],t=e(this).find(".pxl-image__item"),s=t.data("rotatex_from")||0,o=t.data("rotatey_from")||0,i=t.data("rotatex_to")||0,a=t.data("rotatey_to")||0;gsap.set(n,{rotateX:s,rotateY:o,transformStyle:"preserve-3d",willChange:"transform",transition:"transform 0.3s ease"}),gsap.to(n,{scrollTrigger:{trigger:this,start:"top 90%",end:"top 40%",scrub:.3}}),gsap.to(n,{rotateX:i,rotateY:a,scrollTrigger:{trigger:this,start:"top bottom",end:"center center",scrub:0}})}),e(".pxl-variant").each(function(){const t=e(this).find(".pxl-variant__item");gsap.to(t,{scrollTrigger:{trigger:this,start:"top bottom",end:"center center",scrub:0,onEnter:()=>{e(this).addClass("active")},onEnterBack:()=>{e(this).addClass("active")},onLeave:()=>{},onLeaveBack:()=>{e(this).removeClass("active")}}})}),e(".add-active").each(function(){const t=e(this);gsap.to(t,{scrollTrigger:{trigger:this,start:"top bottom",end:"center center",scrub:0,onEnter:()=>{t.addClass("active")},onEnterBack:()=>{t.addClass("active")},onLeave:()=>{},onLeaveBack:()=>{t.removeClass("active")}}})}),e(".pxl-link__menu-sticky").each(function(){const n=e(this),t=n.find(".pxl-link__item a");e(window).on("scroll",function(){const s=e(window).scrollTop();let n=null;t.each(function(){const t=e(this),o=t.attr("href"),i=parseInt(t.parent().data("onepage-offset"))||0;if(o.startsWith("#")&&e(o).length){const a=e(o).offset().top;s+i>=a&&(n=t.parent())}}),n&&(t.parent().removeClass("active"),n.addClass("active"))}),e(window).trigger("scroll")}),e(".pxl-sticky-inner-container").each(function(t,n){if(e(this).parent().children(".pxl-sticky-inner-container").length===1||e(this).is(e(this).parent().children(".pxl-sticky-inner-container").last()||window.innerWidth<1024))return;const s=e(n),o=Math.max(.8,Math.min(.99,(+s.data("pxl-sticky-inner-scale-size")||92)/100)),i=+s.data("pxl-sticky-inner-blur-width")||0;gsap.set(n,{scale:1,filter:"blur(0px)"}),ScrollTrigger.create({trigger:n,start:"top 0%",end:"top 90%",scrub:!0,onUpdate:e=>{const t=e.progress,s=1-(1-o)*t,a=i*t;gsap.set(n,{scale:s,filter:`blur(${a}px)`})}})}),e("a:not(.tabs a, .is-one-page)").on("click",function(){e("html").hasClass("html-smooth-scroll")&&(setTimeout(()=>{e("html").css("scroll-behavior","auto")},800),e("html").css("scroll-behavior","smooth"))}),e(".wpcf7-form .wpcf7-select").each(function(){var n,s,o,i,t=e(this),a=t.children("option").length;if(t.hasClass("pxl-select-hidden"))return;t.addClass("pxl-select-hidden"),t.wrap('<div class="pxl-select"></div>'),t.after('<div class="pxl-select-higthlight"></div>'),n=t.next("div.pxl-select-higthlight"),n.text(t.children("option").eq(0).text());for(o=e("<ul />",{class:"pxl-select-options"}).insertAfter(n),s=0;s<a;s++)e("<li />",{text:t.children("option").eq(s).text(),rel:t.children("option").eq(s).val()}).appendTo(o);i=o.children("li"),n.on("click",function(t){t.stopPropagation(),e("div.pxl-select-higthlight.active").not(this).each(function(){e(this).removeClass("active").next("ul.pxl-select-options").addClass("pxl-select-lists-hide")}),e(this).toggleClass("active")}),i.on("click",function(s){s.stopPropagation(),n.text(e(this).text()).removeClass("active"),t.val(e(this).attr("rel"))}),e(document).on("click",function(){n.removeClass("active")})}),e(".woocommerce-ordering .orderby, #pxl-sidebar-area select, .variations_form.cart .variations select, .pxl-open-table select, .pxl-nice-select, .pxl-select-change-counter__select").each(function(){e(this).niceSelect()}),e(".nice-select").each(function(){var n=e(this),t=n.find("span.current");t.length&&/\(\d+$/.test(t.text())&&t.text(t.text()+")"),n.find("li.option").each(function(){var t=e(this);/\(\d+$/.test(t.text())&&t.text(t.text()+")")})}),e(".pxl-overlay").on("click",function(){e(".pxl-overlay").removeClass("active"),e(".pxl-filter__form").removeClass("active")}),e(".pxl-link-to-section .btn").on("click",function(t){var n=e(this).attr("href"),s=e(".pxl-header-elementor-sticky").outerHeight();t.preventDefault(),e("html, body").animate({scrollTop:e(n).offset().top-s},600)}),e(".pxl-overlay-shake").on("mousemove",function(t){var n=e(this),s=n.offset(),o=t.pageX-s.left,i=t.pageY-s.top;n.find(".pxl-overlay--color").css({transform:`translate(${o}px, ${i}px)`})}),e(".pxl-hover-item").on("mouseenter",function(){e(this).closest(".pxl-hover-wrap").find(".pxl-hover-item").removeClass("pxl-active"),e(this).addClass("pxl-active")}),e("li.pxl-megamenu").on("mouseenter",function(){e(this).closest(".elementor-container").addClass("section-mega-active")}),e("li.pxl-megamenu").on("mouseleave",function(){e(this).closest(".elementor-container").removeClass("section-mega-active")})});function i(){e("#pxl-header-elementor").hasClass("is-sticky")&&(t>100?(e(".pxl-header-elementor-sticky.pxl-sticky-stb").addClass("pxl-header-fixed"),e("#pxl-header-mobile").addClass("pxl-header-mobile-fixed")):(e(".pxl-header-elementor-sticky.pxl-sticky-stb").removeClass("pxl-header-fixed"),e("#pxl-header-mobile").removeClass("pxl-header-mobile-fixed")),o=="up"&&t>100?e(".pxl-header-elementor-sticky.pxl-sticky-stt").addClass("pxl-header-fixed"):e(".pxl-header-elementor-sticky.pxl-sticky-stt").removeClass("pxl-header-fixed")),e(".pxl-header-elementor-sticky").parents("body").addClass("pxl-header-sticky")}function c(){if(e(".px-header--left_sidebar").hasClass("px-header-sidebar-style2")){var n=e(".h5-section-top").outerHeight()+50;console.log(n),t>n?e(".px-header--left_sidebar").addClass("px-header--left_shadow"):e(".px-header--left_sidebar").removeClass("px-header--left_shadow")}}function l(){var t=e("#pxl-header-elementor").outerHeight();s<1199&&e("#pxl-header-elementor").css("min-height",t+"px")}function d(){t<n&&e(".pxl-scroll-top").addClass("pxl-off").removeClass("pxl-on"),t>n&&e(".pxl-scroll-top").addClass("pxl-on").removeClass("pxl-off")}function u(){setTimeout(function(){var t=e(".pxl-footer-fixed #pxl-footer-elementor").outerHeight()-1;e(".pxl-footer-fixed #pxl-main").css("margin-bottom",t+"px")},600)}function h(){var t=e(".pxl-header-elementor-main, .pxl-header-elementor-sticky");t.find(".pxl-menu__primary li").each(function(){var t=e(this).find("> ul.sub-menu");t.length==1&&t.offset().left+t.width()+0>e(window).width()&&t.addClass("pxl-sub-reverse")})}function m(){e(".pxl-menu.divider-move").each(function(){var n,t=e(this).find(".pxl-menu__primary > .current-menu-item, .pxl-menu__primary > .current-menu-parent, .pxl-menu__primary > .current-menu-ancestor");t.length>0?(n=e(this).find(".pxl-menu__divider"),n.css({left:t.position().left,width:t.outerWidth(),display:"block"}),n.addClass("active"),t.addClass("pxl-shape-active"),Modernizr.csstransitions&&(e(this).find(".pxl-menu__primary > li").mouseover(function(){var s=e(this),o=s.position().left,i=s.outerWidth()||t.outerWidth(),a=o==0?0:o||t.position().left;n.css({left:a,width:i}),n.addClass("active"),t.removeClass("pxl-shape-active")}),e(this).find(".pxl-menu__primary").mouseleave(function(){n.css({left:t.position().left,width:t.outerWidth()}),t.addClass("pxl-shape-active")}))):(n=e(this).find(".pxl-menu__divider"),t=e(this).find(".pxl-menu__primary > li:nth-child(1)"),n.css({left:t.position().left,width:t.outerWidth(),display:"block"}),Modernizr.csstransitions&&(e(this).find(".pxl-menu__primary > li").mouseover(function(){var s=e(this),o=s.position().left,i=s.outerWidth()||t.outerWidth(),a=o==0?0:o||t.position().left;n.css({left:a,width:i}),n.addClass("active")}),e(this).find(".pxl-menu__primary").mouseleave(function(){n.css({left:t.position().left,width:t.outerWidth()}),n.removeClass("active")})))})}function p(){"use strict";e(document).on("click",".pxl-anchor-button",function(t){t.preventDefault(),t.stopPropagation();var n=e(this).attr("data-target");e(n).toggleClass("active"),e("body").addClass("body-overflow"),e(n).find(".pxl-search-form").length>0&&setTimeout(function(){e(n).find(".pxl-search-form .pxl-search-field").focus()},1e3)}),e(".pxl-anchor-button").each(function(){var t=e(this).attr("data-target"),n=e(this).attr("data-delay-hover");e(t).find(".pxl-popup__content").css("transition-delay",n+"ms"),e(t).find(".pxl-popup__overlay").css("transition-delay",n+"ms")}),e(document).on("click",".pxl-popup__overlay, .pxl-popup__close",function(){e("body").removeClass("body-overflow"),e(this).parents(".pxl-popup").removeClass("active")}),e(".pxl-button.btn__popup").on("click",function(){e("body").addClass("body-overflow"),e(this).closest(".pxl-wapper").find(".pxl-popup--page").addClass("active")}),e(document).on("click",".pxl-popup--close, .pxl-popup__overlay, .pxl-popup__close",function(){e("body").removeClass("body-overflow"),e(this).closest(".pxl-popup--page").removeClass("active")})}function r(){setTimeout(function(){e(".pxl-swiper-slider__item-inner").each(function(){var t=e(this).outerWidth(),n=e(this).outerHeight();e(this).find(".pxl-item--imgfilter").css("width",t+"px"),e(this).find(".pxl-item--imgfilter").css("height",n+"px")})},300)}function f(){var s=e("#pxl-ptit-elementor.pxl-scroll-opacity .elementor-widget"),n=e("#pxl-ptit-elementor.pxl-scroll-opacity").outerHeight();t<=n&&s.css({opacity:1-t/n})}e.fn.extend({jQueryImagesLoaded:function(){var t,n=this.find('img[src!=""]');return n.length?(t=[],n.each(function(){var n,s=e.Deferred();t.push(s),n=new Image,n.onload=function(){s.resolve()},n.onerror=function(){s.resolve()},n.src=this.src}),e.when.apply(e,t)):e.Deferred().resolve().promise()}});function g(){e(".btn-text-parallax").on("mouseenter",function(){e(this).addClass("hovered")}),e(".btn-text-parallax").on("mouseleave",function(){e(this).removeClass("hovered")}),e(".btn-text-parallax").on("mousemove",function(t){const n=this.getBoundingClientRect(),i=n.left+n.width/2,a=n.top+n.height,s=Math.floor(i-t.clientX)*.172,o=Math.floor(a-t.clientY)*.273;e(this).find(".pxl--btn-text").css({transform:"translate3d("+s*.32+"px, "+o*.32+"px, 0px)"}),e(this).css({transform:"translate3d("+s*.25+"px, "+o*.25+"px, 0px)"})}),e(".el-parallax-wrap").each(function(){e(this).on("mouseenter",function(){e(this).addClass("hovered")}),e(this).on("mouseleave",function(){e(this).removeClass("hovered")}),e(this).on("mousemove",function(t){const n=this.getBoundingClientRect(),s=n.left+n.width/2,o=n.top+n.height,i=Math.floor(s-t.clientX)*.222,a=Math.floor(o-t.clientY)*.333;e(this).find(".el-parallax-item").css({transform:"translate3d("+i*.32+"px, "+a*.32+"px, 0px)"})})}),e(".pxl-hover-parallax").on("mousemove",function(t){const n=this.getBoundingClientRect(),s=n.left+n.width/2,o=n.top+n.height,i=Math.floor(s-t.clientX)*.222,a=Math.floor(o-t.clientY)*.333;e(this).find(".pxl-item-parallax").css({transform:"translate3d("+i*.32+"px, "+a*.32+"px, 0px)"})})}function v(){const t=e(".pxl-scroll-top");if(t.length>0){const n=document.querySelector(".pxl-scroll-progress-circle path"),s=n.getTotalLength();n.style.transition="none",n.style.strokeDasharray=`${s} ${s}`,n.style.strokeDashoffset=s,n.getBoundingClientRect(),n.style.transition="stroke-dashoffset 10ms linear";const o=()=>{const e=window.scrollY,t=document.documentElement.scrollHeight-window.innerHeight,o=s-e*s/t;n.style.strokeDashoffset=o},i=()=>{const e=50;window.scrollY>e?t.addClass("active-progress"):t.removeClass("active-progress")};e(window).on("scroll",()=>{o(),i()}),o(),i()}}function b(){e(".pxl-zoom-point").each(function(){let n=e(this).data("offset"),t=e(this).data("scale-mount");function s(){const s=document.querySelectorAll("[data-scroll-zoom]");let o=0;t=t/100;const i={rootMargin:"0% 0% 0% 0%",threshold:0};s.forEach(n=>{let s=!1;const a=new IntersectionObserver((e)=>{e.forEach(e=>{s=e.isIntersecting})},i);a.observe(n),n.style.transform=`scale(${1+t*e(n)})`,window.addEventListener("scroll",()=>{s&&(o=window.pageYOffset,n.style.transform=`scale(${1+t*e(n)})`)})});function e(e){const s=e.parentNode,o=window.innerHeight,t=window.scrollY,i=s.getBoundingClientRect().top+t+n,c=parseFloat(getComputedStyle(s).getPropertyValue("border-bottom-width"))+parseFloat(getComputedStyle(e).getPropertyValue("border-top-width")),r=s.offsetHeight+c;if(i>t+o)return 0;if(i+r<t)return 100;const l=t+o-i;let a=l/((o+r)/100);return a=Math.round(a),a}}s()})}function j(){if(e("body").hasClass("body-smooth-scroll")){gsap.registerPlugin(ScrollTrigger,ScrollSmoother);const e=ScrollSmoother.create({smooth:1,effects:!0,smoothTouch:.1})}}function y(t){const n=t.is("[data-show]")?t:t.find("[data-show]").first();if(!n.length)return;const r=parseInt(n.attr("data-show"),10)||3,i=n.find("> .pxl-marquee__item");if(!i.length)return;let s=n.closest(".pxl-marquee__viewport, [data-marquee-viewport]").first();s.length||(s=n.parents().filter(function(){const e=getComputedStyle(this);return/(hidden|clip|auto|scroll)/.test(e.overflowX)}).first()),s.length||(s=n.parent());const o=s[0].getBoundingClientRect(),c=(o.left+o.right)/2,a=[];if(i.each(function(){const e=this.getBoundingClientRect(),t=!(e.right<=o.left||e.left>=o.right);if(!t)return;const n=(e.left+e.right)/2,s=Math.abs(n-c);a.push({el:this,distance:s})}),i.removeClass("active active-center"),!a.length)return;a.sort((e,t)=>e.distance-t.distance).slice(0,Math.max(1,r)).forEach((t,n)=>{const s=e(t.el).addClass("active");n===0&&s.addClass("active-center")})}function _(){e(".pxl-marquee__style-2").each(function(){e(this).find(".pxl-marquee__list[data-show]").each(function(){y(e(this))})})}function w(t,n,s){e.ajax({type:"POST",url:loadmore_params.ajax_url,data:{action:"check_more_posts",page:t},success:function(t){t.success||(e(".pxl-posts").css("padding-bottom","0"),n.remove()),s&&s()},error:function(e){console.log(e)}})}})(jQuery),jQuery(document).ready(function(e){e('.pxl-tabs--layout-3-form .pxl-tabs__input-number-group input[type="radio"]').on("change",function(){t(this),e(".pxl-tabs--layout-3-form .pxl-tabs__input-number-group .pxl-tabs__input-number").removeClass("active"),e(this).parent().addClass("active")}),e('.pxl-tabs--layout-3-form .pxl-tabs__switch-currency-item input[type="radio"]').on("change",function(){t(this)}),e('.pxl-tabs--layout-3-form .pxl-tabs__input-number-custom input[type="number"]').on("input",function(){t(this)}),e(".pxl-tabs--layout-3-form #toggleTab").on("change",function(){t(this)});function t(t){var s,o,l,d,n=e(t).closest("form"),i=n.find('.pxl-tabs__switch-currency-item input[type="radio"]:checked').data("symbol"),a=n.find('.pxl-tabs__switch-currency-item input[type="radio"]:checked').data("price-for-plan"),r=n.find('.pxl-tabs__switch-currency-item input[type="radio"]:checked').data("price-for-plan-annual"),c=n.find('.pxl-tabs__input-number-group input[type="radio"]:checked').val(),u=n.find('.pxl-tabs__input-number-custom input[type="number"]').val();e(".pxl-tabs--layout-3-form .pxl-tabs__item.active .pxl-pricing__option").each(function(){const t=e(this),n=t.data("st");t.find(".pxl-pricing__option-text").text(n)}),s=n.find("#toggleTab").is(":checked"),o=c==="custom"?parseInt(u)||1:parseInt(c),l=a?a.split(",").map(e=>parseFloat(e.trim())):[],d=r?r.split(",").map(e=>parseFloat(e.trim())):[],e(".pxl-tabs--layout-3-form .pxl-tabs__item.active .pxl-pricing").each(function(t){var u=l[t]||0,h=d[t]||0,n=u*o,a=h*o,r=e(this).find(".pxl-pricing__price"),c=r.find("[data-price-st][data-price-nd]");c.attr("data-price-st",n).attr("data-price-nd",a),c.text(s?a:n),s?r.find(".pxl-pricing__price-detail").html(`${i}<span data-price-nd="${n}" data-price-st="${a}">${s?a:n}</span>`):r.find(".pxl-pricing__price-detail").html(`${i}<span data-price-st="${n}" data-price-nd="${a}">${s?a:n}</span>`)})}}),function(e){function t(e,t){let n=e.closest(".e-con").siblings(".e-con").find(".pxl-step-image__list").first();n.length||(n=e.closest(".elementor-element").parent().find(".pxl-step-image__list").first());const s=n.children("li");if(!s.length)return;const o=Math.max(0,Math.min(t,s.length-1));s.removeClass("active").eq(o).addClass("active")}function n(n){const f=n.find(".pxl-step__feature"),s=f.find(".pxl-step__feature-item");s.each(function(){const t=e(this),n=t.find(".pxl-step__feature-desc"),s=t.find(".pxl-step__feature-title");s.attr({role:"button",tabindex:0,"aria-expanded":t.hasClass("active")}),n.css("display",t.hasClass("active")?"block":"none")});function l(e){const o=e.index(),i=e.find(".pxl-step__feature-desc");s.not(e).removeClass("active").find(".pxl-step__feature-desc").stop(!0,!0).slideUp(220),s.not(e).find(".pxl-step__feature-title").attr("aria-expanded","false"),e.addClass("active"),e.find(".pxl-step__feature-title").attr("aria-expanded","true"),i.stop(!0,!0).slideDown(220),t(n,o)}function v(e){if(e.hasClass("active"))return;l(e)}const m=n.hasClass("auto-play");let c=!1,r=!1;const b=3e3;let o=Math.max(0,s.filter(".active").index()),a=null;function d(){a&&(clearTimeout(a),a=null)}function u(){return m&&c&&!r}function i(e){if(typeof e=="number"&&!Number.isNaN(e))o=e;else{const e=s.filter(".active").index();e>=0&&(o=e)}if(d(),!u())return;a=setTimeout(function(){o=(o+1)%s.length,l(s.eq(o)),i()},b)}f.off("click.pxlStepSync").on("click.pxlStepSync",".pxl-step__feature-item",function(t){if(e(t.target).closest("a, button").length)return;const n=e(this),s=n.index();v(n),m&&i(s)});const p=s.filter(".active").first();p.length?t(n,p.index()):s.length&&l(s.eq(0)),n.on("mouseenter",function(){r=!0,d()}).on("mouseleave",function(){r=!1,u()&&i()});function h(e){c=e,c?u()&&i():d()}if("IntersectionObserver"in window){const e=new IntersectionObserver(e=>{e.forEach(e=>{e.target===n[0]&&h(e.isIntersecting&&e.intersectionRatio>=.2)})},{root:null,threshold:[0,.2,.5,1],rootMargin:"0px 0px -100px 0px"});e.observe(n[0]),setTimeout(()=>{const e=n[0].getBoundingClientRect(),t=window.innerHeight||document.documentElement.clientHeight,s=e.top<t-100&&e.bottom>0;h(s)},0)}else{function g(){const e=n[0].getBoundingClientRect(),t=window.innerHeight||document.documentElement.clientHeight,s=e.top<t-100&&e.bottom>0;h(s)}e(window).on("scroll.pxlStepSync resize.pxlStepSync",g),g()}}e(function(){e(".pxl-step__style-3").each(function(){n(e(this))})})}(jQuery)