(function(e){e(document).ready(function(){var t={showLogs:!1,round:1e3,init:function(){if(this._log("init"),this._inited){this._log("Already Inited"),this._inited=!0;return}this._requestAnimationFrame=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}(),this._onScroll(!0)},_inited:!1,_properties:["x","y","z","rotateX","rotateY","rotateZ","scaleX","scaleY","scaleZ","scale"],_requestAnimationFrame:null,_log:function(e){this.showLogs&&console.log("Parallax Scroll / "+e)},_onScroll:function(t){var n=e(document).scrollTop(),s=e(window).height();this._log("onScroll "+n),e("[data-parallax]").each(e.proxy(function(o,i){var c,l,d,u,h,m,f,p,g,v,b,j,y,_,w,x,C,k,A,S,M,r=e(i),a=[],E=!1,O=r.data("style");O==null&&(O=r.attr("style")||"",r.data("style",O)),w=[r.data("parallax")];for(g=2;;g++)if(r.data("parallax"+g))w.push(r.data("parallax-"+g));else break;k=w.length;for(g=0;g<k;g++)l=w[g],d=l["from-scroll"],C=l["from-scroll-custom"],d==null&&(C!=null?d=Math.max(0,e(i).offset().top-C):d=Math.max(0,e(i).offset().top-s)),d=d|0,v=l.distance,f=l["to-scroll"],v==null&&f==null&&(v=s),v=Math.max(v|0,1),m=l.easing,y=l["easing-return"],(m==null||!e.easing||!e.easing[m])&&(m=null),(y==null||!e.easing||!e.easing[y])&&(y=m),m&&(h=l.duration,h==null&&(h=v),h=Math.max(h|0,1),_=l["duration-return"],_==null&&(_=h),v=1,c=r.data("current-time"),c==null&&(c=0)),f==null&&(f=d+v),f=f|0,u=l.smoothness,u==null&&(u=30),u=u|0,(t||u==0)&&(u=1),u=u|0,p=n,p=Math.max(p,d),p=Math.min(p,f),m&&(r.data("sens")==void 0&&r.data("sens","back"),p>d&&(r.data("sens")=="back"?(c=1,r.data("sens","go")):c++),p<f&&(r.data("sens")=="go"?(c=1,r.data("sens","back")):c++),t&&(c=h),r.data("current-time",c)),this._properties.map(e.proxy(function(t){var s,o,g,v,i=0,n=l[t];if(n==null)return;t=="scale"||t=="scaleX"||t=="scaleY"||t=="scaleZ"?i=1:n=n|0,s=r.data("_"+t),s==null&&(s=i),g=(n-i)*((p-d)/(f-d))+i,o=s+(g-s)/u,m&&c>0&&c<=h&&(v=i,r.data("sens")=="back"&&(v=n,n=-n,m=y,h=_),o=e.easing[m](null,c,v,n,h)),o=Math.ceil(o*this.round)/this.round,o==s&&g==n&&(o=n),a[t]||(a[t]=0),a[t]+=o,s!=a[t]&&(r.data("_"+t,a[t]),E=!0)},this));E&&(a.z!=void 0&&(j=l.perspective,j==null&&(j=800),b=r.parent(),b.data("style")||b.data("style",b.attr("style")||""),b.attr("style","perspective:"+j+"px; -webkit-perspective:"+j+"px; "+b.data("style"))),a.scaleX==void 0&&(a.scaleX=1),a.scaleY==void 0&&(a.scaleY=1),a.scaleZ==void 0&&(a.scaleZ=1),a.scale!=void 0&&(a.scaleX*=a.scale,a.scaleY*=a.scale,a.scaleZ*=a.scale),A="translate3d("+(a.x?a.x:0)+"px, "+(a.y?a.y:0)+"px, "+(a.z?a.z:0)+"px)",S="rotateX("+(a.rotateX?a.rotateX:0)+"deg) rotateY("+(a.rotateY?a.rotateY:0)+"deg) rotateZ("+(a.rotateZ?a.rotateZ:0)+"deg)",M="scaleX("+a.scaleX+") scaleY("+a.scaleY+") scaleZ("+a.scaleZ+")",x=A+" "+S+" "+M+";",this._log(x),r.attr("style","transform:"+x+" -webkit-transform:"+x+" "+O))},this)),window.requestAnimationFrame?window.requestAnimationFrame(e.proxy(this._onScroll,this,!1)):this._requestAnimationFrame(e.proxy(this._onScroll,this,!1))}};t.init()})})(jQuery)