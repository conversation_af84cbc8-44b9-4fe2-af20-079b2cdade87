(function(e){"use strict";e(window).on("load",function(){e(".flex-control-nav li:first-child").addClass("active")}),e(document).ready(function(){t()});function t(){e(document).on("click",".quantity-action--up",function(){e(this).parent().find('.quantity input[type="number"]').get(0).stepUp(),e(this).parents(".woocommerce-cart-form").find(".actions .button").removeAttr("disabled")}),e(document).on("click",".quantity-action--down",function(){e(this).parent().find('.quantity input[type="number"]').get(0).stepDown(),e(this).parents(".woocommerce-cart-form").find(".actions .button").removeAttr("disabled")}),e(document).on("click",".quantity-action",function(){var n=e(this).parent().find('input[type="number"]').val(),t=e(this).parents(".product, .woocommerce-product-inner").find(".add_to_cart_button");t.attr("data-quantity",n),t.attr("href","?add-to-cart="+t.attr("data-product_id")+"&quantity="+n)}),e(".woocommerce-cart-form .actions .button").removeAttr("disabled")}e(document).on("click",".flex-control-nav li",function(){e(this).addClass("active").siblings().removeClass("active")}),e(document).ajaxComplete(function(){t()}),e(document).on("updated_wc_div",function(){t()})})(jQuery)