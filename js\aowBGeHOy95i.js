(()=>{"use strict";var o,s={d:(e,t)=>{for(var n in t)s.o(t,n)&&!s.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};function n(e){if(this.formData={},this.tree={},!(e instanceof FormData))return this;this.formData=e;const t=()=>{const e=new Map;return e.largestIndex=0,e.set=function(t,n){""===t?t=e.largestIndex++:/^[0-9]+$/.test(t)&&(t=parseInt(t),e.largestIndex<=t&&(e.largestIndex=t+1)),Map.prototype.set.call(e,t,n)},e};this.tree=t();const n=/^(?<name>[a-z][-a-z0-9_:]*)(?<array>(?:\[(?:[a-z][-a-z0-9_:]*|[0-9]*)\])*)/i;for(const[o,s]of this.formData){const e=o.match(n);if(e)if(""===e.groups.array)this.tree.set(e.groups.name,s);else{const n=[...e.groups.array.matchAll(/\[([a-z][-a-z0-9_:]*|[0-9]*)\]/gi)].map(([e,t])=>t);n.unshift(e.groups.name);const o=n.pop();n.reduce((e,n)=>{if(/^[0-9]+$/.test(n)&&(n=parseInt(n)),e.get(n)instanceof Map)return e.get(n);const s=t();return e.set(n,s),s},this.tree).set(o,s)}}}s.r(t),s.d(t,{all:()=>F,any:()=>T,date:()=>u,dayofweek:()=>p,email:()=>A,enum:()=>f,file:()=>m,maxdate:()=>O,maxfilesize:()=>C,maxitems:()=>v,maxlength:()=>j,maxnumber:()=>_,mindate:()=>d,minfilesize:()=>x,minitems:()=>l,minlength:()=>b,minnumber:()=>y,number:()=>w,required:()=>M,requiredfile:()=>S,stepnumber:()=>E,tel:()=>g,time:()=>h,url:()=>k}),n.prototype.entries=function(){return this.tree.entries()},n.prototype.get=function(e){return this.tree.get(e)},n.prototype.getAll=function(e){if(!this.has(e))return[];const t=e=>{const n=[];if(e instanceof Map)for(const[o,s]of e)n.push(...t(s));else""!==e&&n.push(e);return n};return t(this.get(e))},n.prototype.has=function(e){return this.tree.has(e)},n.prototype.keys=function(){return this.tree.keys()},n.prototype.values=function(){return this.tree.values()};const z=n;function e({rule:e,field:t,error:n,...s}){this.rule=e,this.field=t,this.error=n,this.properties=s}const M=function(t){if(0===t.getAll(this.field).map(e=>e.trim()).filter(e=>""!==e).length)throw new e(this)},S=function(t){if(0===t.getAll(this.field).length)throw new e(this)},A=function(t){if(!t.getAll(this.field).map(e=>e.trim()).filter(e=>""!==e).every(e=>{if(e.length<6)return!1;if(-1===e.indexOf("@",1))return!1;if(e.indexOf("@")!==e.lastIndexOf("@"))return!1;const[s,t]=e.split("@",2);if(!/^[a-zA-Z0-9!#$%&'*+/=?^_`{|}~.-]+$/.test(s))return!1;if(/\.{2,}/.test(t))return!1;if(/(?:^[ \t\n\r\0\x0B.]|[ \t\n\r\0\x0B.]$)/.test(t))return!1;const n=t.split(".");if(n.length<2)return!1;for(const e of n){if(/(?:^[ \t\n\r\0\x0B-]|[ \t\n\r\0\x0B-]$)/.test(e))return!1;if(!/^[a-z0-9-]+$/i.test(e))return!1}return!0}))throw new e(this)},k=function(t){const n=t.getAll(this.field).map(e=>e.trim()).filter(e=>""!==e);if(!n.every(e=>{try{return(e=>-1!==["http","https","ftp","ftps","mailto","news","irc","irc6","ircs","gopher","nntp","feed","telnet","mms","rtsp","sms","svn","tel","fax","xmpp","webcal","urn"].indexOf(e))(new URL(e).protocol.replace(/:$/,""))}catch{return!1}}))throw new e(this)},g=function(t){if(!t.getAll(this.field).map(e=>e.trim()).filter(e=>""!==e).every(e=>(((e=(e=e.replace(/[#*].*$/,"")).replaceAll(/[()/.*#\s-]+/g,"")).startsWith("+")||e.startsWith("00"))&&(e=`+${e.replace(/^[+0]+/,"")}`),!!/^[+]?[0-9]+$/.test(e)&&5<e.length&&e.length<16)))throw new e(this)},w=function(t){if(!t.getAll(this.field).map(e=>e.trim()).filter(e=>""!==e).every(e=>!!/^[-]?[0-9]+(?:[eE][+-]?[0-9]+)?$/.test(e)||!!/^[-]?(?:[0-9]+)?[.][0-9]+(?:[eE][+-]?[0-9]+)?$/.test(e)))throw new e(this)},u=function(t){if(!t.getAll(this.field).map(e=>e.trim()).filter(e=>""!==e).every(e=>{if(!/^[0-9]{4,}-[0-9]{2}-[0-9]{2}$/.test(e))return!1;const t=new Date(e);return!Number.isNaN(t.valueOf())}))throw new e(this)},h=function(t){if(!t.getAll(this.field).map(e=>e.trim()).filter(e=>""!==e).every(e=>{const t=e.match(/^([0-9]{2}):([0-9]{2})(?::([0-9]{2}))?$/);if(!t)return!1;const n=parseInt(t[1]),s=parseInt(t[2]),o=t[3]?parseInt(t[3]):0;return 0<=n&&n<=23&&0<=s&&s<=59&&0<=o&&o<=59}))throw new e(this)},m=function(t){if(!t.getAll(this.field).every(e=>e instanceof File&&this.accept?.some(t=>/^\.[a-z0-9]+$/i.test(t)?e.name.toLowerCase().endsWith(t.toLowerCase()):(e=>{const n=[],t=e.match(/^(?<toplevel>[a-z]+)\/(?<sub>[*]|[a-z0-9.+-]+)$/i);if(t){const e=t.groups.toplevel.toLowerCase(),s=t.groups.sub.toLowerCase();for(const[i,o]of(()=>{const e=new Map;return e.set("jpg|jpeg|jpe","image/jpeg"),e.set("gif","image/gif"),e.set("png","image/png"),e.set("bmp","image/bmp"),e.set("tiff|tif","image/tiff"),e.set("webp","image/webp"),e.set("ico","image/x-icon"),e.set("heic","image/heic"),e.set("asf|asx","video/x-ms-asf"),e.set("wmv","video/x-ms-wmv"),e.set("wmx","video/x-ms-wmx"),e.set("wm","video/x-ms-wm"),e.set("avi","video/avi"),e.set("divx","video/divx"),e.set("flv","video/x-flv"),e.set("mov|qt","video/quicktime"),e.set("mpeg|mpg|mpe","video/mpeg"),e.set("mp4|m4v","video/mp4"),e.set("ogv","video/ogg"),e.set("webm","video/webm"),e.set("mkv","video/x-matroska"),e.set("3gp|3gpp","video/3gpp"),e.set("3g2|3gp2","video/3gpp2"),e.set("txt|asc|c|cc|h|srt","text/plain"),e.set("csv","text/csv"),e.set("tsv","text/tab-separated-values"),e.set("ics","text/calendar"),e.set("rtx","text/richtext"),e.set("css","text/css"),e.set("htm|html","text/html"),e.set("vtt","text/vtt"),e.set("dfxp","application/ttaf+xml"),e.set("mp3|m4a|m4b","audio/mpeg"),e.set("aac","audio/aac"),e.set("ra|ram","audio/x-realaudio"),e.set("wav","audio/wav"),e.set("ogg|oga","audio/ogg"),e.set("flac","audio/flac"),e.set("mid|midi","audio/midi"),e.set("wma","audio/x-ms-wma"),e.set("wax","audio/x-ms-wax"),e.set("mka","audio/x-matroska"),e.set("rtf","application/rtf"),e.set("js","application/javascript"),e.set("pdf","application/pdf"),e.set("swf","application/x-shockwave-flash"),e.set("class","application/java"),e.set("tar","application/x-tar"),e.set("zip","application/zip"),e.set("gz|gzip","application/x-gzip"),e.set("rar","application/rar"),e.set("7z","application/x-7z-compressed"),e.set("exe","application/x-msdownload"),e.set("psd","application/octet-stream"),e.set("xcf","application/octet-stream"),e.set("doc","application/msword"),e.set("pot|pps|ppt","application/vnd.ms-powerpoint"),e.set("wri","application/vnd.ms-write"),e.set("xla|xls|xlt|xlw","application/vnd.ms-excel"),e.set("mdb","application/vnd.ms-access"),e.set("mpp","application/vnd.ms-project"),e.set("docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"),e.set("docm","application/vnd.ms-word.document.macroEnabled.12"),e.set("dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"),e.set("dotm","application/vnd.ms-word.template.macroEnabled.12"),e.set("xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"),e.set("xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"),e.set("xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"),e.set("xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"),e.set("xltm","application/vnd.ms-excel.template.macroEnabled.12"),e.set("xlam","application/vnd.ms-excel.addin.macroEnabled.12"),e.set("pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"),e.set("pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"),e.set("ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"),e.set("ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"),e.set("potx","application/vnd.openxmlformats-officedocument.presentationml.template"),e.set("potm","application/vnd.ms-powerpoint.template.macroEnabled.12"),e.set("ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"),e.set("sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"),e.set("sldm","application/vnd.ms-powerpoint.slide.macroEnabled.12"),e.set("onetoc|onetoc2|onetmp|onepkg","application/onenote"),e.set("oxps","application/oxps"),e.set("xps","application/vnd.ms-xpsdocument"),e.set("odt","application/vnd.oasis.opendocument.text"),e.set("odp","application/vnd.oasis.opendocument.presentation"),e.set("ods","application/vnd.oasis.opendocument.spreadsheet"),e.set("odg","application/vnd.oasis.opendocument.graphics"),e.set("odc","application/vnd.oasis.opendocument.chart"),e.set("odb","application/vnd.oasis.opendocument.database"),e.set("odf","application/vnd.oasis.opendocument.formula"),e.set("wp|wpd","application/wordperfect"),e.set("key","application/vnd.apple.keynote"),e.set("numbers","application/vnd.apple.numbers"),e.set("pages","application/vnd.apple.pages"),e})())("*"===s&&o.startsWith(e+"/")||o===t[0])&&n.push(...i.split("|"))}return n})(t).some(t=>(t="."+t.trim(),e.name.toLowerCase().endsWith(t.toLowerCase()))))))throw new e(this)},f=function(t){if(!t.getAll(this.field).map(e=>e.trim()).filter(e=>""!==e).every(e=>this.accept?.some(t=>e===String(t))))throw new e(this)},p=function(t){if(!t.getAll(this.field).map(e=>e.trim()).filter(e=>""!==e).every(e=>{const n=0===(t=new Date(e).getDay())?7:t;var t;return this.accept?.some(e=>n===parseInt(e))}))throw new e(this)},l=function(t){if(t.getAll(this.field).map(e=>e.trim()).filter(e=>""!==e).length<parseInt(this.threshold))throw new e(this)},v=function(t){const n=t.getAll(this.field).map(e=>e.trim()).filter(e=>""!==e);if(parseInt(this.threshold)<n.length)throw new e(this)},b=function(t){const s=t.getAll(this.field).map(e=>e.trim()).filter(e=>""!==e);let n=0;if(s.forEach(e=>{"string"==typeof e&&(n+=e.length)}),0!==n&&n<parseInt(this.threshold))throw new e(this)},j=function(t){const s=t.getAll(this.field).map(e=>e.trim()).filter(e=>""!==e);let n=0;if(s.forEach(e=>{"string"==typeof e&&(n+=e.length)}),parseInt(this.threshold)<n)throw new e(this)},y=function(t){if(!t.getAll(this.field).map(e=>e.trim()).filter(e=>""!==e).every(e=>!(parseFloat(e)<parseFloat(this.threshold))))throw new e(this)},_=function(t){if(!t.getAll(this.field).map(e=>e.trim()).filter(e=>""!==e).every(e=>!(parseFloat(this.threshold)<parseFloat(e))))throw new e(this)},d=function(t){if(!t.getAll(this.field).map(e=>e.trim()).filter(e=>""!==e).every(e=>!(/^[0-9]{4,}-[0-9]{2}-[0-9]{2}$/.test(e)&&/^[0-9]{4,}-[0-9]{2}-[0-9]{2}$/.test(this.threshold)&&e<this.threshold)))throw new e(this)},O=function(t){if(!t.getAll(this.field).map(e=>e.trim()).filter(e=>""!==e).every(e=>!(/^[0-9]{4,}-[0-9]{2}-[0-9]{2}$/.test(e)&&/^[0-9]{4,}-[0-9]{2}-[0-9]{2}$/.test(this.threshold)&&this.threshold<e)))throw new e(this)},x=function(t){const s=t.getAll(this.field);let n=0;if(s.forEach(e=>{e instanceof File&&(n+=e.size)}),n<parseInt(this.threshold))throw new e(this)},C=function(t){const s=t.getAll(this.field);let n=0;if(s.forEach(e=>{e instanceof File&&(n+=e.size)}),parseInt(this.threshold)<n)throw new e(this)},E=function(t){const s=t.getAll(this.field).map(e=>e.trim()).filter(e=>""!==e),o=parseFloat(this.base),n=parseFloat(this.interval);if(!(0<n))return!0;if(!s.every(e=>{const t=(parseFloat(e)-o)%n;return"0.000000"===Math.abs(t).toFixed(6)||"0.000000"===Math.abs(t-n).toFixed(6)}))throw new e(this)},r=({ruleObj:e,options:n})=>{const{rule:s,...o}=e;return"function"==typeof t[s]&&("function"!=typeof t[s].matches||t[s].matches(o,n))},a=({ruleObj:e,formDataTree:n,options:s})=>{const{rule:o}=e;t[o].call(e,n,s)},c=[],i=e=>[...c].reduce((e,t)=>n=>t(n,e),e),F=function(t,n={}){const s=(this.rules??[]).filter(e=>r({ruleObj:e,options:n})),o=i(a);if(!s.every(s=>{try{o({ruleObj:s,formDataTree:t,options:n})}catch(t){if(!(t instanceof e))throw t;if(void 0!==t.error)throw t;return!1}return!0}))throw new e(this)},T=function(t,n={}){const s=(this.rules??[]).filter(e=>r({ruleObj:e,options:n})),o=i(a);if(!s.some(s=>{try{o({ruleObj:s,formDataTree:t,options:n})}catch(t){if(!(t instanceof e))throw t;return!1}return!0}))throw new e(this)};window.swv={validators:t,validate:(t,n,s={})=>{const l=(t.rules??[]).filter(e=>r({ruleObj:e,options:s}));if(!l.length)return new Map;const d=i(a),o=new z(n),c=l.reduce((t,n)=>{try{d({ruleObj:n,formDataTree:o,options:s})}catch(n){if(!(n instanceof e))throw n;if(void 0!==n.field&&!t.has(n.field)&&void 0!==n.error)return t.set(n.field,n)}return t},new Map);for(const e of o.keys())c.has(e)||c.set(e,{validInputs:o.getAll(e)});return c},use:e=>{c.push(e)},...null!==(o=window.swv)&&void 0!==o?o:{}}})()