jQuery(document).ready(function(e){"use strict";e(document).find(".pxl-user-form-login .login-group").on("keypress","input",function(t){var n=t.keyCode?t.keyCode:t.which;n=="13"&&s(e(this))}),e(document).find(".pxl-user-form-login .login-group").on("keypress","input",function(){t(e(this),"","hide")}),e(document).find(".pxl-user-form").on("click",".button-login",function(t){t.preventDefault(),s(e(this))}),e(document).find(".pxl-user-form-register").on("click",".btn-up-register",function(t){t.preventDefault(),n(e(this))}),e(document).find(".pxl-user-form-register").on("keypress","input",function(){t(e(this),"","hide")}),e(document).find(".pxl-user-form").on("keypress","input",function(t){var s=t.keyCode?t.keyCode:t.which;s=="13"&&n(e(this))});function n(n){var s=n.parents(".pxl-user-form-register"),r=s.find("input#res_user"),l=s.find("input#res_pass1"),d=s.find("input#res_pass2"),c=s.find("input#res_email"),u=a(r,l,d,c);u!==!1&&(o(s),e.post(caseuser.ajax,{action:"form_ajax_register",data:u,_ajax_nonce:caseuser.nonce},function(e){e.error==!0?(e.user_null!=void 0&&t(r,e.user_null),e.pass_null!=void 0&&t(l,e.pass_null),e.email_null!=void 0&&t(c,e.email_null),e.passconfirm!=void 0&&t(d,e.passconfirm),e.user_invalid!=void 0&&t(r,e.user_invalid),e.user_exists!=void 0&&t(r,e.user_exists),e.email_exists!=void 0&&t(c,e.email_exists)):location.reload()}),i(s))}function a(e,n,s,o){var a,i=!0,l=e.val().trim(),c=n.val().trim(),u=s.val().trim(),d=o.val().trim();return l==""?(t(e,e.data("validate")),i=!1):4>l.length&&(t(e,e.data("user-length")),i=!1),c==""?(t(n,n.data("validate")),i=!1):5>c.length?(t(n,n.data("pass-length")),i=!1):c!=u&&(t(s,s.data("pass-confirm")),i=!1),d==""?(t(o,o.data("validate")),i=!1):r(d)===!1&&(t(o,o.data("email-format")),i=!1),i===!0&&(a={},a.user=l,a.pass=c,a.email=d,a.passconfirm=u,a)}function s(n){var s={},a=n.parents(".pxl-user-form"),r=a.find("input.user_name"),c=a.find("input.password"),l=r.val(),d=c.val();l==""&&t(r,r.data("validate")),d==""&&t(c,c.data("validate")),s.user=l,s.pass=d,s.rememberme=a.find("input.rememberme").val(),s.user&&s.pass&&(o(a),e.post(caseuser.ajax,{action:"pxl_user_login",data:s,_ajax_nonce:caseuser.nonce},function(e){e.error==!0?(e.user!=void 0&&t(r,e.user),e.pass!=void 0&&t(c,e.pass)):location.reload(),i(a)}))}function o(t){t.find("input, button, select").each(function(){e(this).prop("disabled",!0)})}function i(t){t.find("input, button, select").each(function(){e(this).prop("disabled",!1)})}function r(e){var t=/^([\w-]+(?:\.[\w-]+)*)@((?:[\w-]+\.)*\w[\w-]{0,66})\.([a-z]{2,6}(?:\.[a-z]{2})?)$/i;return t.test(e)}function t(e,t,n){n=n||"show",n=="show"?e.next().hasClass("wpcf7-not-valid-tip")===!1?(e.after('<span role="alert" class="wpcf7-not-valid-tip" style="display:none">'+t+"</span>"),e.next().fadeIn("slow")):e.parent().find(".wpcf7-not-valid-tip").length>0&&(e.parent().find(".wpcf7-not-valid-tip").remove(),e.after('<span role="alert" class="wpcf7-not-valid-tip" style="display:none">'+t+"</span>"),e.next().fadeIn("slow")):e.parent().find(".wpcf7-not-valid-tip").length>0&&e.parent().find(".wpcf7-not-valid-tip").fadeOut("slow")}})