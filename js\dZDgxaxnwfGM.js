(function(e){"use strict";var t=function(e,t){e.find(".pxl-accordion .pxl-accordion__item-title").on("click",function(e){e.preventDefault();var i,n=t(this),o=n.data("target"),s=t(o),a=n.closest(".pxl-accordion"),r=a.find(".pxl-accordion__item-title");r.each(function(){var e=t(this),n=e.data("target"),s=t(n);n!==o&&(e.removeClass("active").parent().removeClass("active"),s.stop(!0,!0).slideUp(400))}),i=n.parent().hasClass("active"),i?(n.removeClass("active").parent().removeClass("active"),s.stop(!0,!0).slideUp(400)):(n.addClass("active").parent().addClass("active"),s.stop(!0,!0).slideDown(400,function(){s.css("height","")}))})};e(window).on("elementor/frontend/init",function(){elementorFrontend.hooks.addAction("frontend/element_ready/pxl_accordion.default",t)})})(jQuery)