"use strict";(function(e){var n=!0,v=[],t=Cookies.get("woosw_key");try{n="sessionStorage"in window&&window.sessionStorage!==null,window.sessionStorage.setItem("woosw","test"),window.sessionStorage.removeItem("woosw")}catch{n=!1}e(function(){(t==null||t==="")&&(t=y(),Cookies.set("woosw_key",t,{expires:7})),_(),(e(".woosw-custom-menu-item").length||woosw_vars.reload_count==="yes")&&p(),woosw_vars.added_to_cart==="yes"&&woosw_vars.auto_remove==="yes"&&setTimeout(function(){p(),m()},300),woosw_vars.button_action==="message"&&e.notiny.addTheme("woosw",{notification_class:"notiny-theme-woosw"})}),e(document).on("change",".woosw-switcher-dropdown",function(){window.location=e(this).val()}),e(document).on("woosw_refresh_data",function(){m()}),e(document).on("woosw_refresh_count",function(){p()}),e(document).on("woovr_selected",function(t,n){var i=n.attr("data-id"),o=n.attr("data-pid");i>0?(e(".woosw-btn-"+o).attr("data-id",i).removeClass("woosw-btn-added woosw-added"),d(i)):(e(".woosw-btn-"+o).attr("data-id",o).removeClass("woosw-btn-added woosw-added"),d(o))}),e(document).on("found_variation",function(t,n){var s=e(t.target).attr("data-product_id");e(".woosw-btn-"+s).attr("data-id",n.variation_id).removeClass("woosw-btn-added woosw-added"),d(n.variation_id)}),e(document).on("reset_data",function(t){var n=e(t.target).attr("data-product_id");e(".woosw-btn-"+n).attr("data-id",n).removeClass("woosw-btn-added woosw-added"),d(n)}),e(document.body).on("added_to_cart",function(t,n,s,o){if(woosw_vars.auto_remove==="yes"){var i=parseInt(o.data("product_id"));i>0&&e(".woosw-item-"+i).length&&e(".woosw-item-"+i).remove(),p(),m()}}),e(document).on("click touch","#woosw_wishlist .woosq-link, #woosw_wishlist .woosq-btn",function(e){h(),e.preventDefault()}),e(document).on("click touch",".woosw-btn",function(c){var p,l=e(this),h=l.attr("data-id"),g=l.attr("data-pid"),m=l.attr("data-product_id"),v=l.attr("data-product_name"),b=l.attr("data-product_image");typeof g!==typeof void 0&&g!==!1&&(h=g),typeof m!==typeof void 0&&m!==!1&&(h=m),l.hasClass("woosw-added")?woosw_vars.button_action_added==="remove"?(p={action:"woosw_remove",product_id:h,key:t,nonce:woosw_vars.nonce},l.addClass("woosw-adding").find(".woosw-btn-icon").removeClass(woosw_vars.button_normal_icon+" "+woosw_vars.button_added_icon).addClass(woosw_vars.button_loading_icon),e.post(woosw_vars.wc_ajax_url.toString().replace("%%endpoint%%","woosw_remove"),p,function(t){l.removeClass("woosw-adding").find(".woosw-btn-icon").removeClass(woosw_vars.button_loading_icon),t.content!=null&&e("#woosw_wishlist").html(t.content).addClass("woosw-loaded"),t.notice!=null&&u(t.notice),t.count!=null&&s(t.count),n&&t.data&&sessionStorage.setItem("woosw_data_"+t.data.key,JSON.stringify(t.data)),t.data.fragments&&a(t.data.fragments),t.data.ids&&(o(t.data.ids),i(t.data.ids)),e(document.body).trigger("woosw_remove",[m])})):woosw_vars.button_action_added==="page"?window.location.href=woosw_vars.wishlist_url:e("#woosw_wishlist").hasClass("woosw-loaded")?r():f():(p={action:"woosw_add",product_id:h,nonce:woosw_vars.nonce},l.addClass("woosw-adding").find(".woosw-btn-icon").removeClass(woosw_vars.button_normal_icon+" "+woosw_vars.button_added_icon).addClass(woosw_vars.button_loading_icon),e.post(woosw_vars.wc_ajax_url.toString().replace("%%endpoint%%","woosw_add"),p,function(t){l.removeClass("woosw-adding").find(".woosw-btn-icon").removeClass(woosw_vars.button_loading_icon),woosw_vars.button_action==="list"&&(t.content!=null&&e("#woosw_wishlist").html(t.content).addClass("woosw-loaded"),t.notice!=null&&u(t.notice.replace("{name}","<strong>"+v+"</strong>")),j(),r()),woosw_vars.button_action==="message"&&(e("#woosw_wishlist").removeClass("woosw-loaded"),e.notiny({theme:"woosw",position:woosw_vars.message_position,image:b,text:t.notice.replace("{name}","<strong>"+v+"</strong>")})),woosw_vars.button_action==="no"&&e("#woosw_wishlist").removeClass("woosw-loaded"),t.count!=null&&s(t.count),t.status===1&&d(h),n&&t.data&&sessionStorage.setItem("woosw_data_"+t.data.key,JSON.stringify(t.data)),t.data.fragments&&a(t.data.fragments),t.data.ids&&(o(t.data.ids),i(t.data.ids)),e(document.body).trigger("woosw_add",[h])})),c.preventDefault()}),e(document).on("click touch",".woosw-item--add span",function(t){var r=e(this),d=r.closest(".woosw-items").data("key"),c=r.closest(".woosw-item"),l=c.attr("data-id"),h=c.attr("data-product_name"),m={action:"woosw_add",product_id:l,key:d,nonce:woosw_vars.nonce};r.addClass("woosw-item--adding"),e.post(woosw_vars.wc_ajax_url.toString().replace("%%endpoint%%","woosw_add"),m,function(t){r.addClass("woosw-item--adding"),t.content!=null&&e("#woosw_wishlist").html(t.content).addClass("woosw-loaded"),t.notice!=null&&u(t.notice.replace("{name}","<strong>"+h+"</strong>")),t.count!=null&&s(t.count),n&&t.data&&sessionStorage.setItem("woosw_data_"+t.data.key,JSON.stringify(t.data)),t.data.fragments&&a(t.data.fragments),t.data.ids&&(o(t.data.ids),i(t.data.ids)),e(document.body).trigger("woosw_add",[l]),r.closest(".woosw-list").length&&location.reload()}),t.preventDefault()}),e(document).on("click touch",".woosw-item--remove span",function(t){var r=e(this),d=r.closest(".woosw-items").data("key"),c=r.closest(".woosw-item"),l=c.attr("data-id"),h={action:"woosw_remove",product_id:l,key:d,nonce:woosw_vars.nonce};r.addClass("woosw-item--removing"),e.post(woosw_vars.wc_ajax_url.toString().replace("%%endpoint%%","woosw_remove"),h,function(t){r.removeClass("woosw-item--removing"),c.remove(),t.content!=null&&e("#woosw_wishlist").html(t.content).addClass("woosw-loaded"),t.notice!=null&&u(t.notice),t.count!=null&&s(t.count),n&&t.data&&sessionStorage.setItem("woosw_data_"+t.data.key,JSON.stringify(t.data)),t.data.fragments&&a(t.data.fragments),t.data.ids&&(o(t.data.ids),i(t.data.ids)),e(document.body).trigger("woosw_remove",[l])}),t.preventDefault()}),e(document).on("click touch",".woosw-empty",function(t){var r,d,h=e(this);confirm(woosw_vars.empty_confirm)&&(c(),r=h.data("key"),d={action:"woosw_empty",key:r,nonce:woosw_vars.nonce},e.post(woosw_vars.wc_ajax_url.toString().replace("%%endpoint%%","woosw_empty"),d,function(t){t.content!=null&&e("#woosw_wishlist").html(t.content).addClass("woosw-loaded"),t.notice!=null&&u(t.notice),t.count!=null&&s(t.count),n&&t.data&&sessionStorage.setItem("woosw_data_"+t.data.key,JSON.stringify(t.data)),t.data.fragments&&a(t.data.fragments),t.data.ids&&(o(t.data.ids),i(t.data.ids)),l(),e(document.body).trigger("woosw_empty",[r])})),t.preventDefault()}),e(document).on("click touch",".woosw-popup",function(t){var n=e(".woosw-popup-content");e(t.target).closest(n).length||(h(),g())}),e(document).on("click touch",".woosw-continue",function(t){var n=e(this).attr("data-url");h(),n!==""&&(window.location.href=n),t.preventDefault()}),e(document).on("click touch","#woosw_wishlist .woosw-popup-close",function(e){h(),e.preventDefault()}),e(document).on("click touch","#woosw_manage .woosw-popup-close",function(e){g(),e.preventDefault()}),e(document).on("click touch",".woosw-manage",function(t){t.preventDefault(),c();var n={action:"woosw_manage_wishlists",nonce:woosw_vars.nonce};e.post(woosw_vars.wc_ajax_url.toString().replace("%%endpoint%%","woosw_manage_wishlists"),n,function(t){h(),e("#woosw_manage").html(t),O(),l()})}),e(document).on("click touch","#woosw_add_wishlist",function(t){t.preventDefault(),c();var n=e("#woosw_wishlist_name").val(),s={action:"woosw_add_wishlist",name:n,nonce:woosw_vars.nonce};e.post(woosw_vars.wc_ajax_url.toString().replace("%%endpoint%%","woosw_add_wishlist"),s,function(t){e("#woosw_manage").html(t),e("#woosw_wishlist").removeClass("woosw-loaded"),l()})}),e(document).on("click touch",".woosw-set-default",function(t){t.preventDefault(),c();var r=e(this).data("key"),u={action:"woosw_set_default",key:r,nonce:woosw_vars.nonce};e.post(woosw_vars.wc_ajax_url.toString().replace("%%endpoint%%","woosw_set_default"),u,function(t){t.count!=null&&s(t.count),t.products!=null&&t.products.length&&t.products.forEach(e=>{d(e)}),e("#woosw_manage").html(t.content),n&&t.data&&sessionStorage.setItem("woosw_data_"+t.data.key,JSON.stringify(t.data)),t.data.fragments&&a(t.data.fragments),t.data.ids&&(o(t.data.ids),i(t.data.ids)),e("#woosw_wishlist").removeClass("woosw-loaded"),l()})}),e(document).on("click touch",".woosw-delete-wishlist",function(t){if(t.preventDefault(),confirm(woosw_vars.delete_confirm)){c();var n=e(this).data("key"),s={action:"woosw_delete_wishlist",key:n,nonce:woosw_vars.nonce};e.post(woosw_vars.wc_ajax_url.toString().replace("%%endpoint%%","woosw_delete_wishlist"),s,function(t){e("#woosw_manage").html(t),e("#woosw_wishlist").removeClass("woosw-loaded"),l()})}}),e(document).on("click touch",".woosw-view-wishlist",function(t){t.preventDefault(),c();var n=e(this).data("key"),s={action:"woosw_view_wishlist",key:n,nonce:woosw_vars.nonce};e.post(woosw_vars.wc_ajax_url.toString().replace("%%endpoint%%","woosw_view_wishlist"),s,function(t){g(),e("#woosw_wishlist").removeClass("woosw-loaded").html(t),r(),l()})}),e(document).on("click touch",".woosw-menu-item a, .woosw-menu a, .woosw-link.woosw-link-auto a",function(t){woosw_vars.menu_action==="open_popup"&&(t.preventDefault(),e("#woosw_wishlist").hasClass("woosw-loaded")?r():f())}),e(document).on("click touch",".woosw-link.woosw-link-popup a",function(t){t.preventDefault(),e("#woosw_wishlist").hasClass("woosw-loaded")?r():f()}),e(document).on("click touch",".woocommerce-MyAccount-navigation-link--wishlist a",function(t){woosw_vars.page_myaccount==="yes_popup"&&(t.preventDefault(),e("#woosw_wishlist").hasClass("woosw-loaded")?r():f())}),e(document).on("click touch","#woosw_copy_url, #woosw_copy_btn",function(t){t.preventDefault();let n=e("#woosw_copy_url"),s=n.val();navigator.clipboard.writeText(s).then(function(){alert(woosw_vars.copied_text+" "+s)},function(){alert("Failure to copy!")}),n.select()}),e(document).on("click touch",".woosw-item--note",function(){e(this).closest(".woosw-item").find(".woosw-item--note-add").length&&(e(this).closest(".woosw-item").find(".woosw-item--note-add").show(),e(this).hide())}),e(document).on("click touch",".woosw_add_note",function(t){t.preventDefault(),c();var n=e(this),s=n.closest(".woosw-items").data("key"),o=n.closest(".woosw-item").attr("data-id"),i=n.closest(".woosw-item").find('input[type="text"]').val(),a={action:"woosw_add_note",key:s,product_id:o,note:w(i),nonce:woosw_vars.nonce};e.post(woosw_vars.wc_ajax_url.toString().replace("%%endpoint%%","woosw_add_note"),a,function(e){n.closest(".woosw-item").find(".woosw-item--note").html(e).show(),n.closest(".woosw-item").find(".woosw-item--note-add").hide(),l()})}),e(window).on("resize",function(){b()});function f(){var t={action:"woosw_load",nonce:woosw_vars.nonce};e.post(woosw_vars.wc_ajax_url.toString().replace("%%endpoint%%","woosw_load"),t,function(t){t.content!=null&&e("#woosw_wishlist").html(t.content),t.count!=null&&s(e("#woosw_wishlist .woosw-items:not(.woosw-suggested-items) .woosw-item").length&&e("#woosw_wishlist .woosw-items:not(.woosw-suggested-items) .woosw-item").length!=t.count?e("#woosw_wishlist .woosw-items:not(.woosw-suggested-items) .woosw-item").length:t.count),t.notice!=null&&u(t.notice),e("#woosw_wishlist").addClass("woosw-loaded"),j(),r()})}function p(){var t={action:"woosw_load_count",nonce:woosw_vars.nonce};e.post(woosw_vars.wc_ajax_url.toString().replace("%%endpoint%%","woosw_load_count"),t,function(t){if(t.count!=null){var n=t.count;s(n),e(document.body).trigger("woosw_load_count",[n])}})}function r(){e("#woosw_wishlist").addClass("woosw-show"),b(),e(document.body).trigger("woosw_wishlist_show")}function h(){e("#woosw_wishlist").removeClass("woosw-show"),e(document.body).trigger("woosw_wishlist_hide")}function O(){e("#woosw_manage").addClass("woosw-show"),e(document.body).trigger("woosw_manage_show")}function g(){e("#woosw_manage").removeClass("woosw-show"),e(document.body).trigger("woosw_manage_hide")}function c(){e(".woosw-popup").addClass("woosw-loading")}function l(){e(".woosw-popup").removeClass("woosw-loading")}function s(t){e("#woosw_wishlist .woosw-count").html(t),e(".woosw-link .woosw-link-inner").attr("data-count",t),parseInt(t)>0?e(".woosw-empty").show():e(".woosw-empty").hide(),e(".woosw-menu-item .woosw-menu-item-inner").length?e(".woosw-menu-item .woosw-menu-item-inner").attr("data-count",t):e(".woosw-menu-item a").html('<span class="woosw-menu-item-inner" data-count="'+t+'"><i class="woosw-icon"></i><span>'+woosw_vars.menu_text+"</span></span>"),e(document.body).trigger("woosw_change_count",[t])}function u(t){e(".woosw-notice").html(t),C(),setTimeout(function(){x()},3e3)}function C(){e("#woosw_wishlist .woosw-notice").addClass("woosw-notice-show")}function x(){e("#woosw_wishlist .woosw-notice").removeClass("woosw-notice-show")}function j(){woosw_vars.perfect_scrollbar==="yes"&&jQuery("#woosw_wishlist .woosw-popup-content-mid").perfectScrollbar({theme:"wpc"})}function w(e){return String(e).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;")}function y(){for(var e=[],t="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",s=t.length,n=0;n<6;n++)e.push(t.charAt(Math.floor(Math.random()*s)));return e.join("")}function b(){jQuery(".woosw-popup-center .woosw-popup-content").height(2*Math.floor(jQuery(".woosw-popup-center .woosw-popup-content").height()/2)+2)}function _(){if(n)try{var e=JSON.parse(sessionStorage.getItem("woosw_data_"+t));e.fragments&&a(e.fragments),e.ids&&(o(e.ids),i(e.ids)),e.key&&(t==null||t==="")&&Cookies.set("woosw_key",e.key,{expires:7})}catch{m()}else m()}function m(){var s={action:"woosw_get_data",nonce:woosw_vars.nonce};e.post(woosw_vars.wc_ajax_url.toString().replace("%%endpoint%%","woosw_get_data"),s,function(s){s&&(n&&sessionStorage.setItem("woosw_data_"+s.key,JSON.stringify(s)),s.fragments&&a(s.fragments),s.ids&&(o(s.ids),i(s.ids)),s.key&&(t==null||t===""||t!==s.key)&&Cookies.set("woosw_key",s.key,{expires:7}),e(document.body).trigger("woosw_data_refreshed",[s]))})}function a(t){e.each(t,function(t,n){e(t).replaceWith(n)}),e(document.body).trigger("woosw_fragments_refreshed",[t])}function i(e){v=e}function o(t){e(".woosw-btn").removeClass("woosw-btn-added woosw-added"),e(".woosw-btn:not(.woosw-btn-has-icon)").text(woosw_vars.button_text),e(".woosw-btn.woosw-btn-has-icon").find(".woosw-btn-icon").removeClass(woosw_vars.button_added_icon).addClass(woosw_vars.button_normal_icon),e(".woosw-btn.woosw-btn-has-icon").find(".woosw-btn-text").text(woosw_vars.button_text),e.each(t,function(t,n){e(".woosw-btn-"+t).addClass("woosw-btn-added woosw-added"),e(".woosw-btn-"+t+":not(.woosw-btn-has-icon)").text(woosw_vars.button_text_added),e(".woosw-btn-has-icon.woosw-btn-"+t).find(".woosw-btn-icon").removeClass(woosw_vars.button_normal_icon).addClass(woosw_vars.button_added_icon),e(".woosw-btn-has-icon.woosw-btn-"+t).find(".woosw-btn-text").text(woosw_vars.button_text_added),n.parent!==void 0&&(e(".woosw-btn-"+n.parent).addClass("woosw-btn-added woosw-added"),e(".woosw-btn-"+n.parent+":not(.woosw-btn-has-icon)").text(woosw_vars.button_text_added),e(".woosw-btn-has-icon.woosw-btn-"+n.parent).find(".woosw-btn-icon").removeClass(woosw_vars.button_normal_icon).addClass(woosw_vars.button_added_icon),e(".woosw-btn-has-icon.woosw-btn-"+n.parent).find(".woosw-btn-text").text(woosw_vars.button_text_added))}),e(document.body).trigger("woosw_buttons_refreshed",[t])}function d(t){e('.woosw-btn[data-id="'+t+'"]').removeClass("woosw-btn-added woosw-added"),e('.woosw-btn[data-id="'+t+'"]:not(.woosw-btn-has-icon)').text(woosw_vars.button_text),e('.woosw-btn-has-icon.woosw-btn[data-id="'+t+'"]').find(".woosw-btn-icon").removeClass(woosw_vars.button_added_icon).addClass(woosw_vars.button_normal_icon),e('.woosw-btn-has-icon.woosw-btn[data-id="'+t+'"]').find(".woosw-btn-text").text(woosw_vars.button_text),e.each(v,function(n){parseInt(n)===parseInt(t)&&(e('.woosw-btn[data-id="'+t+'"]').addClass("woosw-btn-added woosw-added"),e('.woosw-btn[data-id="'+t+'"]:not(.woosw-btn-has-icon)').text(woosw_vars.button_text_added),e('.woosw-btn-has-icon.woosw-btn[data-id="'+t+'"]').find(".woosw-btn-icon").removeClass(woosw_vars.button_normal_icon).addClass(woosw_vars.button_added_icon),e('.woosw-btn-has-icon.woosw-btn[data-id="'+t+'"]').find(".woosw-btn-text").text(woosw_vars.button_text_added))}),e(document.body).trigger("woosw_refresh_button_id",[t,v])}})(jQuery)