(function(e){e.fn.animatedHeadline=function(t){var n=e.extend({animationType:"rotate-1",animationDelay:2500,barAnimationDelay:3800,barWaiting:800,lettersDelay:50,typeLettersDelay:150,selectionDuration:500,typeAnimationDelay:1300,revealDuration:600,revealAnimationDelay:1500},t),c=n.animationDelay;this.each(function(){if(t=e(this),n.animationType&&(n.animationType=="type"||n.animationType=="rotate-2"||n.animationType=="rotate-3"||n.animationType=="scale"?t.find(".pxl-heading .pxl-heading__title").addClass("letters "+n.animationType):n.animationType=="clip"?t.find(".pxl-heading .pxl-heading__title").addClass(n.animationType+" is-full-width"):t.find(".pxl-heading .pxl-heading__title").addClass(n.animationType)),u(e(".pxl-heading .pxl-heading__title.letters").find("b")),t.hasClass("loading-bar"))c=n.barAnimationDelay,setTimeout(function(){t.find(".pxl-heading__type-loading-bar").addClass("is-loading")},n.barWaiting);else if(t.hasClass("clip")){var t,o,a,i=t.find(".pxl-heading__type-clip"),r=i.width()+10;i.css("width",r)}else t.find(".pxl-heading .pxl-heading__title").hasClass("type")||(a=t.find("b"),o=0,a.each(function(){var t=e(this).width();t>o&&(o=t)}),t.find(".pxl-heading__words").css("width",o));setTimeout(function(){s(t.find(".is-visible").eq(0))},c)});function u(t){t.each(function(){var s,n=e(this),t=n.text().split(""),o=n.hasClass("is-visible");for(i in t)n.parents(".rotate-2").length>0&&(t[i]="<em>"+t[i]+"</em>"),t[i]=o?'<i class="in">'+t[i]+"</i>":"<i>"+t[i]+"</i>";s=t.join(""),n.html(s).css("opacity",1)})}function s(e){var i,c,t=r(e);e.parents(".pxl-heading .pxl-heading__title").hasClass("type")?(i=e.parent(".pxl-heading__words"),i.addClass("selected").removeClass("waiting"),setTimeout(function(){i.removeClass("selected"),e.removeClass("is-visible").addClass("is-hidden").children("i").removeClass("in").addClass("out")},n.selectionDuration),setTimeout(function(){l(t,n.typeLettersDelay)},n.typeAnimationDelay)):e.parents(".pxl-heading .pxl-heading__title").hasClass("letters")?(c=e.children("i").length>=t.children("i").length,d(e.find("i").eq(0),e,c,n.lettersDelay),a(t.find("i").eq(0),t,c,n.lettersDelay)):e.parents(".pxl-heading .pxl-heading__title").hasClass("clip")?e.parents(".pxl-heading__words").animate({width:"2px"},n.revealDuration,function(){o(e,t),l(t)}):e.parents(".pxl-heading .pxl-heading__title").hasClass("loading-bar")?(e.parents(".pxl-heading__words").removeClass("is-loading"),o(e,t),setTimeout(function(){s(t)},n.barAnimationDelay),setTimeout(function(){e.parents(".pxl-heading__words").addClass("is-loading")},n.barWaiting)):(o(e,t),setTimeout(function(){s(t)},n.animationDelay))}function l(e,t){e.parents(".pxl-heading .pxl-heading__title").hasClass("type")?(a(e.find("i").eq(0),e,!1,t),e.addClass("is-visible").removeClass("is-hidden")):e.parents(".pxl-heading .pxl-heading__title").hasClass("clip")&&e.parents(".pxl-heading__words").animate({width:e.width()+10},n.revealDuration,function(){setTimeout(function(){s(e)},n.revealAnimationDelay)})}function d(t,i,a,c){if(t.removeClass("in").addClass("out"),t.is(":last-child")?a&&setTimeout(function(){s(r(i))},n.animationDelay):setTimeout(function(){d(t.next(),i,a,c)},c),t.is(":last-child")&&e("html").hasClass("no-csstransitions")){var l=r(i);o(i,l)}}function a(e,t,o,i){e.addClass("in").removeClass("out"),e.is(":last-child")?(t.parents(".pxl-heading .pxl-heading__title").hasClass("type")&&setTimeout(function(){t.parents(".pxl-heading__words").addClass("waiting")},200),o||setTimeout(function(){s(t)},n.animationDelay)):setTimeout(function(){a(e.next(),t,o,i)},i)}function r(e){var t=e.parent().children("b"),n=t.index(e);return n+1<t.length?t.eq(n+1):t.eq(0)}function h(e){var t=e.parent().children("b"),n=t.index(e);return n-1>=0?t.eq(n-1):t.last()}function o(e,t){e.removeClass("is-visible").addClass("is-hidden"),t.removeClass("is-hidden").addClass("is-visible")}}})(jQuery)