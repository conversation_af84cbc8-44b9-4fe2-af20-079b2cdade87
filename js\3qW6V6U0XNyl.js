(function(e){"use strict";function t(){e(".pxl-accordion").each(function(){var n,t=e(this);t.find(".pxl-accordion__item").removeClass("active"),t.find(".pxl-accordion__item-content").hide(),n=t.find(".pxl-accordion__item:first-child"),n.addClass("active"),n.find(".pxl-accordion__item-content").show()})}function n(){e(".pxl-accordion__item-title").off("click"),e(".pxl-accordion__item-title").on("click",function(t){t.preventDefault();var i=e(this),n=i.parent(".pxl-accordion__item"),s=n.find(".pxl-accordion__item-content"),o=n.parent(".pxl-accordion");n.hasClass("active")?(n.removeClass("active"),s.slideUp(400),e(this).parent().index()>0&&(n.prev().removeClass("active-prev"),n.next().removeClass("active-next"))):(o.find(".pxl-accordion__item").removeClass("active active-prev active-next"),o.find(".pxl-accordion__item-content").slideUp(400),n.addClass("active"),s.slideDown(400),e(this).parent().index()>0&&(n.prev().addClass("active-prev"),n.next().addClass("active-next")))})}var s=function(e,t){e.find(".pxl-tabs.tab-effect-slide .pxl-tabs__list-item").on("click",function(e){e.preventDefault();var s=t(this).data("target"),n=t(this).parents(".pxl-tabs");n.find(".pxl-tabs__content>.pxl-tabs__item").slideUp(400),n.find(".pxl-tabs__list .pxl-tabs__list-item").removeClass("active"),t(this).addClass("active"),t(s).slideDown(400)}),e.find(".pxl-tabs.tab-effect-fade .pxl-tabs__list-item").on("click",function(e){e.preventDefault();var s=t(this).data("target"),n=t(this).parents(".pxl-tabs");n.find(".pxl-tabs__content>.pxl-tabs__item").hide(),n.find(".pxl-tabs__list .pxl-tabs__list-item").removeClass("active"),t(this).addClass("active"),t(s).show()}),e.find(".pxl-tabs").each(function(){var s,o,i,a,r,c,d,n=t(this),e=n.find(".pxl-tabs__item-all"),l=n.find(".pxl-tabs__list");if(e.length>0){d=e.html(),e.empty(),a=n.find(".pxl-tabs__item:not(.pxl-tabs__item-all)").eq(0),a.length>0&&a.children().clone(!0).appendTo(e),r=e.find(".elementor-widget-container > div"),r.length>0&&n.find(".pxl-tabs__item:not(.pxl-tabs__item-all)").slice(1).each(function(){t(this).find(".elementor-widget-container > div > div").each(function(){t(this).clone(!0).appendTo(r)})}),s=n.find(".pxl-tabs__faqs-loadmore"),s.length>0&&(i=e.find(".pxl-accordion"),i.append(s),o=parseInt(e.data("show"))||6,c=i.find(".pxl-accordion__item"),c.length>o?(c.slice(o).hide(),s.show(),s.find(".pxl-tabs__loadmore").off("click").on("click",function(e){e.preventDefault();var a,n=t(this).find(".pxl-tabs__loadmore"),r=n.data("loading-text")||"Loading",c=n.text();n.find("span").text(r),n.addClass("loading"),a=i.find(".pxl-accordion__item:hidden"),setTimeout(function(){a.slice(0,o).fadeIn(0),n.removeClass("loading"),a.length<=o?(n.parent().remove(),setTimeout(function(){s.fadeOut()},3e3)):n.find("span").text(c)},0)})):s.hide());function u(){var n=e.find(".pxl-accordion");n.each(function(){var e=t(this);e.find(".pxl-accordion__item").removeClass("active").find(".pxl-accordion__item-content").hide(),e.find(".pxl-accordion__item:first-child").addClass("active").find(".pxl-accordion__item-content").show()})}u()}l.data("count")==="yes"&&n.find(".pxl-tabs__list-item").each(function(){const e=t(this).index(),s=n.find(".pxl-tabs__content .pxl-tabs__item").eq(e).find(".pxl-accordion__item").length;t(this).append(`<span class="item-count">(${s})</span>`)})})},o=function(e,t){e.find(".pxl-accordion").each(function(){var e=t(this);e.find(".pxl-accordion__item").removeClass("active"),e.find(".pxl-accordion__item-content").hide(),e.find(".pxl-accordion__item:first-child").addClass("active").find(".pxl-accordion__item-content").show()}),e.find(".pxl-accordion__item-title").off("click").on("click",function(e){e.preventDefault();var i=t(this),n=i.parent(".pxl-accordion__item"),s=n.find(".pxl-accordion__item-content"),o=n.parent(".pxl-accordion");n.hasClass("active")?(n.removeClass("active"),s.slideUp(400)):(o.find(".pxl-accordion__item").removeClass("active"),o.find(".pxl-accordion__item-content").slideUp(400),n.addClass("active"),s.slideDown(400))})};e(function(){t(),n()}),e(document).ready(function(){t(),n(),e(".pxl-tabs").each(function(){var t=e(this);t.find("#toggleTab").on("change",function(){let n=e(this).closest(".pxl-tabs__switch-btn"),s=t.find(".pxl-tabs__content"),o=s.find(".pxl-tabs__item"),i=n.data("st"),a=n.data("nd");o.toggleClass("active"),o.filter(".active").prependTo(s),e(this).is(":checked")?t.find(".pxl-tabs__switch-txt").text(a):t.find(".pxl-tabs__switch-txt").text(i)}),s({find:function(e){return t.find(e)}},e)})}),e(window).on("elementor/frontend/init",function(){elementorFrontend.hooks.addAction("frontend/element_ready/pxl_tabs.default",s),elementorFrontend.hooks.addAction("frontend/element_ready/pxl_banner_tabs.default",s),elementorFrontend.hooks.addAction("frontend/element_ready/pxl_accordion.default",o),elementorFrontend.hooks.addAction("frontend/element_ready/global",function(){t(),n()})}),e(document).ajaxComplete(function(){setTimeout(function(){t(),n()},100)})})(jQuery)