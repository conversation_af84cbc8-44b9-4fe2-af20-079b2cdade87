<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Automation SaaS – BrightHub Clone (100% Free)</title>
    
    <!-- FREE Google Fonts (same as original) -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&family=Arapey:ital,wght@0,400;1,400&family=Allura&family=Rubik:wght@300..900&family=Geist:wght@100..900&display=swap" rel="stylesheet">
    
    <!-- FREE Font Awesome (instead of Pro) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- FREE AOS Animation Library (instead of GSAP) -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <!-- FREE Swiper.js (instead of paid sliders) -->
    <link rel="stylesheet" href="https://unpkg.com/swiper@8/swiper-bundle.min.css">
    
    <style>
        /* EXACT COLOR SCHEME FROM ORIGINAL */
        :root {
            --primary-color: #181D27;
            --secondary-color: #7A5AF8;
            --third-color: #535862;
            --body-bg-color: #fff;
            --primary-color-rgb: 24,29,39;
            --secondary-color-rgb: 122,90,248;
            --third-color-rgb: 83,88,98;
            --link-color: #181D27;
            --link-color-hover: #7A5AF8;
            --gradient-color-from: #181D27;
            --gradient-color-to: #7A5AF8;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--body-bg-color);
            color: var(--primary-color);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* SMOOTH SCROLLING (FREE CSS instead of Lenis) */
        html {
            scroll-behavior: smooth;
        }

        /* HEADER STYLES */
        .header {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            z-index: 1000;
            padding: 1rem 0;
            transition: all 0.3s ease;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            text-decoration: none;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-menu a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-menu a:hover {
            color: var(--secondary-color);
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--gradient-color-from), var(--gradient-color-to));
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(122, 90, 248, 0.3);
        }

        .btn-outline {
            background: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .btn-outline:hover {
            background: var(--primary-color);
            color: white;
        }

        /* HERO SECTION */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            padding: 8rem 2rem 4rem;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .hero-container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
        }

        .hero-content h1 {
            font-size: 3.5rem;
            font-weight: 700;
            line-height: 1.2;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-content p {
            font-size: 1.25rem;
            color: var(--third-color);
            margin-bottom: 2rem;
        }

        .hero-buttons {
            display: flex;
            gap: 1rem;
            margin-bottom: 3rem;
        }

        .hero-image {
            position: relative;
        }

        .hero-image img {
            width: 100%;
            height: auto;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }

        /* FEATURES SECTION */
        .features {
            padding: 6rem 2rem;
            background: white;
        }

        .features-container {
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }

        .section-subtitle {
            font-size: 1.25rem;
            color: var(--third-color);
            margin-bottom: 4rem;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 4rem;
        }

        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            border: 1px solid #f1f5f9;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 60px rgba(122, 90, 248, 0.1);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--secondary-color), #9333ea);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 1.5rem;
        }

        .feature-card h3 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }

        .feature-card p {
            color: var(--third-color);
            line-height: 1.6;
        }

        /* PRICING SECTION */
        .pricing {
            padding: 6rem 2rem;
            background: #f8fafc;
        }

        .pricing-container {
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 4rem;
        }

        .pricing-card {
            background: white;
            padding: 3rem 2rem;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            position: relative;
        }

        .pricing-card.featured {
            border: 2px solid var(--secondary-color);
            transform: scale(1.05);
        }

        .pricing-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }

        .pricing-card.featured:hover {
            transform: scale(1.05) translateY(-10px);
        }

        .price {
            font-size: 3rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .price span {
            font-size: 1rem;
            color: var(--third-color);
        }

        /* RESPONSIVE DESIGN */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .hero-container {
                grid-template-columns: 1fr;
                text-align: center;
            }

            .hero-content h1 {
                font-size: 2.5rem;
            }

            .features-grid,
            .pricing-grid {
                grid-template-columns: 1fr;
            }
        }

        /* ANIMATION CLASSES (FREE CSS animations instead of GSAP) */
        .fade-in-up {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .fade-in-up.animate {
            opacity: 1;
            transform: translateY(0);
        }

        .fade-in-left {
            opacity: 0;
            transform: translateX(-30px);
            transition: all 0.6s ease;
        }

        .fade-in-left.animate {
            opacity: 1;
            transform: translateX(0);
        }

        .fade-in-right {
            opacity: 0;
            transform: translateX(30px);
            transition: all 0.6s ease;
        }

        .fade-in-right.animate {
            opacity: 1;
            transform: translateX(0);
        }
    </style>
</head>
<body>
    <!-- HEADER -->
    <header class="header">
        <nav class="nav-container">
            <a href="#" class="logo">BrightHub</a>
            <ul class="nav-menu">
                <li><a href="#home">Home</a></li>
                <li><a href="#about">About Us</a></li>
                <li><a href="#features">Features</a></li>
                <li><a href="#pricing">Pricing</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
            <div class="nav-buttons">
                <a href="#" class="btn btn-outline">Log In</a>
                <a href="#" class="btn btn-primary">Try for Free</a>
            </div>
        </nav>
    </header>

    <!-- HERO SECTION -->
    <section id="home" class="hero">
        <div class="hero-container">
            <div class="hero-content" data-aos="fade-right">
                <h1>Automate Your Workflow Save Time Effortlessly</h1>
                <p>No-Code AI-powered automation for businesses of all sizes</p>
                <div class="hero-buttons">
                    <a href="#" class="btn btn-primary">
                        <i class="fas fa-rocket"></i> Get Started
                    </a>
                    <a href="#" class="btn btn-outline">
                        See How It Works
                    </a>
                </div>
                <p><small>Trusted by Thousands of Businesses</small></p>
            </div>
            <div class="hero-image" data-aos="fade-left">
                <div style="width: 100%; height: 400px; background: linear-gradient(135deg, var(--secondary-color), #9333ea); border-radius: 20px; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem;">
                    <i class="fas fa-cogs"></i>
                </div>
            </div>
        </div>
    </section>

    <!-- FEATURES SECTION -->
    <section id="features" class="features">
        <div class="features-container">
            <h2 class="section-title" data-aos="fade-up">Powerful Features</h2>
            <p class="section-subtitle" data-aos="fade-up" data-aos-delay="100">Everything you need to automate your business processes</p>

            <div class="features-grid">
                <div class="feature-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="feature-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3>AI-Powered Automation</h3>
                    <p>Intelligent automation that learns and adapts to your business needs without any coding required.</p>
                </div>

                <div class="feature-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="feature-icon">
                        <i class="fas fa-link"></i>
                    </div>
                    <h3>Seamless Integrations</h3>
                    <p>Connect with 1000+ apps and services. From CRM to email marketing, we've got you covered.</p>
                </div>

                <div class="feature-card" data-aos="fade-up" data-aos-delay="400">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3>Real-time Analytics</h3>
                    <p>Track performance, monitor workflows, and get insights to optimize your automation processes.</p>
                </div>

                <div class="feature-card" data-aos="fade-up" data-aos-delay="500">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>Enterprise Security</h3>
                    <p>Bank-level security with end-to-end encryption to keep your data safe and compliant.</p>
                </div>

                <div class="feature-card" data-aos="fade-up" data-aos-delay="600">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3>Team Collaboration</h3>
                    <p>Work together seamlessly with role-based access and real-time collaboration features.</p>
                </div>

                <div class="feature-card" data-aos="fade-up" data-aos-delay="700">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>Mobile Ready</h3>
                    <p>Manage your automations on the go with our responsive design and mobile apps.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- PRICING SECTION -->
    <section id="pricing" class="pricing">
        <div class="pricing-container">
            <h2 class="section-title" data-aos="fade-up">Simple Pricing</h2>
            <p class="section-subtitle" data-aos="fade-up" data-aos-delay="100">Choose the plan that fits your needs</p>

            <div class="pricing-grid">
                <div class="pricing-card" data-aos="fade-up" data-aos-delay="200">
                    <h3>Starter</h3>
                    <div class="price">$29<span>/month</span></div>
                    <p>Perfect for small teams</p>
                    <ul style="list-style: none; padding: 0; margin: 2rem 0;">
                        <li style="padding: 0.5rem 0;"><i class="fas fa-check" style="color: var(--secondary-color); margin-right: 0.5rem;"></i> 100 Automations</li>
                        <li style="padding: 0.5rem 0;"><i class="fas fa-check" style="color: var(--secondary-color); margin-right: 0.5rem;"></i> 5 Team Members</li>
                        <li style="padding: 0.5rem 0;"><i class="fas fa-check" style="color: var(--secondary-color); margin-right: 0.5rem;"></i> Basic Support</li>
                    </ul>
                    <a href="#" class="btn btn-outline" style="width: 100%;">Get Started</a>
                </div>

                <div class="pricing-card featured" data-aos="fade-up" data-aos-delay="300">
                    <div style="position: absolute; top: -10px; left: 50%; transform: translateX(-50%); background: var(--secondary-color); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.8rem;">Most Popular</div>
                    <h3>Professional</h3>
                    <div class="price">$99<span>/month</span></div>
                    <p>For growing businesses</p>
                    <ul style="list-style: none; padding: 0; margin: 2rem 0;">
                        <li style="padding: 0.5rem 0;"><i class="fas fa-check" style="color: var(--secondary-color); margin-right: 0.5rem;"></i> Unlimited Automations</li>
                        <li style="padding: 0.5rem 0;"><i class="fas fa-check" style="color: var(--secondary-color); margin-right: 0.5rem;"></i> 25 Team Members</li>
                        <li style="padding: 0.5rem 0;"><i class="fas fa-check" style="color: var(--secondary-color); margin-right: 0.5rem;"></i> Priority Support</li>
                        <li style="padding: 0.5rem 0;"><i class="fas fa-check" style="color: var(--secondary-color); margin-right: 0.5rem;"></i> Advanced Analytics</li>
                    </ul>
                    <a href="#" class="btn btn-primary" style="width: 100%;">Get Started</a>
                </div>

                <div class="pricing-card" data-aos="fade-up" data-aos-delay="400">
                    <h3>Enterprise</h3>
                    <div class="price">$299<span>/month</span></div>
                    <p>For large organizations</p>
                    <ul style="list-style: none; padding: 0; margin: 2rem 0;">
                        <li style="padding: 0.5rem 0;"><i class="fas fa-check" style="color: var(--secondary-color); margin-right: 0.5rem;"></i> Everything in Pro</li>
                        <li style="padding: 0.5rem 0;"><i class="fas fa-check" style="color: var(--secondary-color); margin-right: 0.5rem;"></i> Unlimited Team Members</li>
                        <li style="padding: 0.5rem 0;"><i class="fas fa-check" style="color: var(--secondary-color); margin-right: 0.5rem;"></i> 24/7 Phone Support</li>
                        <li style="padding: 0.5rem 0;"><i class="fas fa-check" style="color: var(--secondary-color); margin-right: 0.5rem;"></i> Custom Integrations</li>
                    </ul>
                    <a href="#" class="btn btn-outline" style="width: 100%;">Contact Sales</a>
                </div>
            </div>
        </div>
    </section>

    <!-- FOOTER -->
    <footer style="background: var(--primary-color); color: white; padding: 4rem 2rem 2rem; text-align: center;">
        <div style="max-width: 1200px; margin: 0 auto;">
            <h3 style="margin-bottom: 1rem;">BrightHub</h3>
            <p style="color: #94a3b8; margin-bottom: 2rem;">Automate your workflow, save time effortlessly</p>
            <div style="display: flex; justify-content: center; gap: 2rem; margin-bottom: 2rem;">
                <a href="#" style="color: #94a3b8; text-decoration: none;">Privacy Policy</a>
                <a href="#" style="color: #94a3b8; text-decoration: none;">Terms of Service</a>
                <a href="#" style="color: #94a3b8; text-decoration: none;">Contact</a>
            </div>
            <p style="color: #64748b; font-size: 0.9rem;">&copy; 2025 BrightHub Clone. 100% Free & Legal Recreation.</p>
        </div>
    </footer>

    <!-- FREE JavaScript Libraries -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="https://unpkg.com/swiper@8/swiper-bundle.min.js"></script>

    <script>
        // Initialize AOS (Animate On Scroll) - FREE alternative to GSAP
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            offset: 100
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Header scroll effect
        window.addEventListener('scroll', () => {
            const header = document.querySelector('.header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.98)';
                header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.boxShadow = 'none';
            }
        });

        // Add some interactive hover effects
        document.querySelectorAll('.feature-card, .pricing-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px)';
            });

            card.addEventListener('mouseleave', function() {
                if (!this.classList.contains('featured')) {
                    this.style.transform = 'translateY(0)';
                } else {
                    this.style.transform = 'scale(1.05)';
                }
            });
        });
    </script>
</body>
</html>
