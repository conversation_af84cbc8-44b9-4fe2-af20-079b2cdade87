!function(){function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,i)}return a}function a(t){for(var a=1;a<arguments.length;a++){var i=null!=arguments[a]?arguments[a]:{};a%2?e(Object(i),!0).forEach((function(e){r(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):e(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function i(t,e){var a="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!a){if(Array.isArray(t)||(a=l(t))||e&&t&&"number"==typeof t.length){a&&(t=a);var i=0,n=function(){};return{s:n,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,r=!0,s=!1;return{s:function(){a=a.call(t)},n:function(){var t=a.next();return r=t.done,t},e:function(t){s=!0,o=t},f:function(){try{r||null==a.return||a.return()}finally{if(s)throw o}}}}function n(t){return function(t){if(Array.isArray(t))return c(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||l(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(t,e){for(var a=0;a<e.length;a++){var i=e[a];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function r(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function s(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var a=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=a){var i,n,o=[],r=!0,s=!1;try{for(a=a.call(t);!(r=(i=a.next()).done)&&(o.push(i.value),!e||o.length!==e);r=!0);}catch(t){s=!0,n=t}finally{try{r||null==a.return||a.return()}finally{if(s)throw n}}return o}}(t,e)||l(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(t,e){if(t){if("string"==typeof t)return c(t,e);var a=Object.prototype.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?c(t,e):void 0}}function c(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,i=new Array(e);a<e;a++)i[a]=t[a];return i}!function(e){"use strict";function l(t){if(!t)return!1;if("boolean"==typeof t)return t;var e=String(t).toLowerCase().trim();return!!["1","true","yes","on"].includes(e)||1===t}e.createMiddlewareForExtraQueryParams=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return function(e,a){if(function(t){return!!t.path&&-1!==t.path.indexOf("woo-variation-swatches")||!!t.url&&-1!==t.url.indexOf("woo-variation-swatches")}(e)&&Object.keys(t).length>0)for(var i=0,n=Object.entries(t);i<n.length;i++){var o=s(n[i],2),l=o[0],c=o[1];"string"!=typeof e.url||wp.url.hasQueryArg(e.url,l)||(e.url=wp.url.addQueryArgs(e.url,r({},l,c))),"string"!=typeof e.path||wp.url.hasQueryArg(e.path,l)||(e.path=wp.url.addQueryArgs(e.path,r({},l,c)))}return a(e)}};var c,u=(c=jQuery,function(){function t(e,a,i){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),r(this,"defaults",{}),this.name=i,this.element=e,this.$element=c(e),this.settings=c.extend(!0,{},this.defaults,a),this.product_variations=this.$element.data("product_variations")||[],this.is_ajax_variation=this.product_variations.length<1,this.product_id=this.$element.data("product_id"),this.reset_variations=this.$element.find(".reset_variations"),this.attributeFields=this.$element.find(".variations select"),this.attributeSwatchesFields=this.$element.find("ul.variable-items-wrapper"),this.selected_item_template='<span class="woo-selected-variation-item-name" data-default=""></span>',this.$element.addClass("wvs-loaded"),this.init(),this.update(),c(document).trigger("woo_variation_swatches_loaded",this)}var u,d,v;return u=t,(d=[{key:"isAjaxVariation",value:function(){return this.is_ajax_variation}},{key:"init",value:function(){this.prepareLabel(),this.prepareItems(),this.setupItems(),this.setupEvents(),this.setUpStockInfo(),this.deselectNonAvailable()}},{key:"prepareLabel",value:function(){var t=this;l(woo_variation_swatches_options.show_variation_label)&&this.$element.find(".variations .label").each((function(e,a){0===c(a).find(".woo-selected-variation-item-name").length&&c(a).append(t.selected_item_template)}))}},{key:"prepareItems",value:function(){this.attributeSwatchesFields.each((function(t,e){c(e).parent().addClass("woo-variation-items-wrapper")}))}},{key:"setupItems",value:function(){var t=this;this.attributeSwatchesFields.each((function(e,a){var i="",o=c(a).parent().find("select.woo-variation-raw-select"),r=o.find("option"),s=o.find("option:disabled"),l=o.find("option.enabled.out-of-stock"),u=o.find("option:selected"),d=o.find("option").eq(1),v=(c(a).data("attribute_name"),c(a).data("attribute_values")),f=[],h=[],p=[];r.each((function(){""!==c(this).val()&&(f.push(c(this).val()),i=0===u.length?d.val():u.val())})),s.each((function(){""!==c(this).val()&&p.push(c(this).val())})),l.each((function(){""!==c(this).val()&&h.push(c(this).val())}));var m=_.difference(f,p),b=[].concat(n(_.difference(v,f)),p);t.setupItem(a,i,m,h,b)}))}},{key:"setupItem",value:function(t,e,a,i,n){var o=this,r=c(t).parent().prev().find(".woo-selected-variation-item-name");c(t).find("li.variable-item").each((function(t,s){var u=c(s).attr("data-value"),d=c(s).attr("data-title");c(s).removeClass("selected disabled no-stock").addClass("disabled"),c(s).attr("aria-checked","false"),c(s).attr("tabindex","-1"),c(s).attr("data-wvstooltip-out-of-stock",""),c(s).find("input.variable-item-radio-input:radio").prop("disabled",!0).prop("checked",!1),e.length<1&&l(woo_variation_swatches_options.show_variation_label)&&r.text(""),o.isAjaxVariation()?(c(s).find("input.variable-item-radio-input:radio").prop("disabled",!1),c(s).removeClass("selected disabled no-stock"),u===e&&(c(s).addClass("selected"),c(s).attr("aria-checked","true"),c(s).attr("tabindex","0"),c(s).find("input.variable-item-radio-input:radio").prop("disabled",!1).prop("checked",!0),l(woo_variation_swatches_options.show_variation_label)&&r.text("".concat(woo_variation_swatches_options.variation_label_separator," ").concat(d)),c(s).trigger("wvs-item-updated",[e,u]))):(_.includes(a,u)&&(c(s).removeClass("selected disabled"),c(s).removeAttr("aria-hidden"),c(s).attr("tabindex","0"),c(s).find("input.variable-item-radio-input:radio").prop("disabled",!1),u===e&&(c(s).addClass("selected"),c(s).attr("aria-checked","true"),c(s).find("input.variable-item-radio-input:radio").prop("checked",!0),l(woo_variation_swatches_options.show_variation_label)&&r.text("".concat(woo_variation_swatches_options.variation_label_separator," ").concat(d)),c(s).trigger("wvs-item-updated",[e,u]))),_.includes(i,u)&&(c(s).attr("data-wvstooltip-out-of-stock",woo_variation_swatches_options.out_of_stock_tooltip_text),l(woo_variation_swatches_options.clickable_out_of_stock)&&c(s).removeClass("disabled").addClass("no-stock")),_.includes(n,u)&&c(s).attr("data-wvstooltip-out-of-stock",woo_variation_swatches_options.unavailable_tooltip_text))}))}},{key:"setupEvents",value:function(){var t=this.$element;this.attributeSwatchesFields.each((function(e,a){var i=c(a).parent().find("select.woo-variation-raw-select");l(woo_variation_swatches_options.clear_on_reselect)?(c(a).on("click.wvs","li.variable-item:not(.selected):not(.radio-variable-item)",(function(e){e.preventDefault(),e.stopPropagation();var a=c(this).data("attribute_name"),n=c(this).data("value");i.val(n).trigger("change"),i.trigger("click"),c(this).trigger("wvs-selected-item",[a,n,i,t])})),c(a).on("click.wvs","li.variable-item.selected:not(.radio-variable-item)",(function(e){e.preventDefault(),e.stopPropagation();var a=c(this).data("attribute_name"),n=c(this).data("value");i.val("").trigger("change"),i.trigger("click"),c(this).trigger("wvs-unselected-item",[a,n,i,t])})),c(a).on("click.wvs","input.variable-item-radio-input:radio",(function(t){t.stopPropagation(),c(this).trigger("change.wvs",{radioChange:!0})})),c(a).on("change.wvs","input.variable-item-radio-input:radio",(function(e,a){if(e.preventDefault(),e.stopPropagation(),a&&a.radioChange){var n=c(this).data("attribute_name"),o=c(this).val();c(this).parent("li.radio-variable-item").hasClass("selected")?(i.val("").trigger("change"),c(this).closest("li.radio-variable-item").trigger("wvs-unselected-item",[n,o,i,t])):(i.val(o).trigger("change"),c(this).closest("li.radio-variable-item").trigger("wvs-selected-item",[n,o,i,t])),i.trigger("click")}}))):(c(a).on("click.wvs","li.variable-item:not(.radio-variable-item)",(function(e){e.preventDefault(),e.stopPropagation();var a=c(this).data("attribute_name"),n=c(this).data("value");i.val(n).trigger("change"),i.trigger("click"),c(this).trigger("wvs-selected-item",[a,n,i,t])})),c(a).on("change.wvs","input.variable-item-radio-input:radio",(function(e){e.preventDefault(),e.stopPropagation();var a=c(this).data("attribute_name"),n=c(this).val();i.val(n).trigger("change"),i.trigger("click"),c(this).closest("li.radio-variable-item").removeClass("selected disabled no-stock").addClass("selected"),c(this).closest("li.radio-variable-item").trigger("wvs-selected-item",[a,n,i,t])}))),c(a).on("keydown.wvs","li.variable-item:not(.disabled)",(function(t){(t.keyCode&&32===t.keyCode||t.key&&" "===t.key||t.keyCode&&13===t.keyCode||t.key&&"enter"===t.key.toLowerCase())&&(t.preventDefault(),c(this).trigger("click"))}))})),this.$element.on("click.wvs",".woo-variation-swatches-variable-item-more",(function(t){t.preventDefault(),c(this).parent().removeClass("enabled-display-limit-mode enabled-catalog-display-limit-mode"),c(this).remove()})),this.$element.find("[data-wvstooltip]").each((function(t,a){c(a).on("mouseenter",(function(t){var i=a.getBoundingClientRect(),n=e.getComputedStyle(a,":before"),o=e.getComputedStyle(a,":after"),r=parseInt(o.getPropertyValue("border-top-width"),10),s=parseInt(n.getPropertyValue("height"),10),l=parseInt(n.getPropertyValue("width"),10),c=s+r+2;a.classList.toggle("wvs-tooltip-position-bottom",i.top<c);var u=l/2,d=i.left+i.width/2,v=u-d,f=u>d,h=u+d,p=document.body.clientWidth<h,m=document.body.clientWidth-h;a.style.setProperty("--horizontal-position","0px"),f&&a.style.setProperty("--horizontal-position","".concat(v+2,"px")),p&&a.style.setProperty("--horizontal-position","".concat(m-2,"px"))}))}))}},{key:"extractAttributes",value:function(t){var e,a=new Set,n=i(this.product_variations);try{for(n.s();!(e=n.n()).done;){var o=e.value.attributes;for(var r in o)o[r].length>0&&a.add(r)}}catch(t){n.e(t)}finally{n.f()}return a.delete(t),Array.from(a)}},{key:"getUnavailableAttributes",value:function(t,e){return 0===this.findMatchingVariations(this.product_variations,t).filter((function(t){return!l(woo_variation_swatches_options.disable_out_of_stock)||t.is_in_stock})).length?this.extractAttributes(e):[]}},{key:"deselectNonAvailable",value:function(){var t=this;l(woo_variation_swatches_options.deselect_unavailable)&&this.$element.on("wvs-selected-item.wvs",(function(e,n,o){var s=a(a({},t.getChosenAttributes().data),{},r({},n,o.toString())),l=t.getUnavailableAttributes(s,n);if(l.length>0){var u,d=i(l);try{for(d.s();!(u=d.n()).done;){var v=u.value;t.attributeSwatchesFields.find('li[data-attribute_name="'.concat(v,'"]')).removeClass("selected"),t.attributeFields.filter('[data-attribute_name="'.concat(v,'"]')).each((function(t,e){c(e).val("").trigger("change")}))}}catch(t){d.e(t)}finally{d.f()}t.attributeSwatchesFields.filter('[data-attribute_name="'.concat(n,'"]')).each((function(){c(this).find('[data-value="'.concat(o,'"]')).removeClass("disabled").addClass("selected")})),t.attributeFields.filter('[data-attribute_name="'.concat(n,'"]')).each((function(t,e){c(e).val(o).trigger("change")}))}}))}},{key:"update",value:function(){var t=this;this.$element.on("woocommerce_variation_has_changed.wvs",(function(e){t.setupItems()}))}},{key:"setUpStockInfo",value:function(){var t=this;if(l(woo_variation_swatches_options.show_variation_stock)){var e=parseInt(woo_variation_swatches_options.stock_label_threshold,10);this.$element.on("wvs-selected-item.wvs",(function(a){var i=t.getChosenAttributes(),n=t.findStockVariations(t.product_variations,i);i.count>1&&i.count===i.chosenCount&&t.resetStockInfo(),i.count>1&&i.count===i.mayChosenCount&&n.forEach((function(a){var i='[data-attribute_name="'.concat(a.attribute_name,'"] > [data-value="').concat(a.attribute_value,'"]');a.variation.is_in_stock&&a.variation.max_qty&&a.variation.variation_stock_left&&a.variation.max_qty<=e?(t.$element.find("".concat(i," .wvs-stock-left-info")).attr("data-wvs-stock-info",a.variation.variation_stock_left),t.$element.find(i).addClass("wvs-show-stock-left-info")):(t.$element.find(i).removeClass("wvs-show-stock-left-info"),t.$element.find("".concat(i," .wvs-stock-left-info")).attr("data-wvs-stock-info",""))}))})),this.$element.on("hide_variation.wvs",(function(){t.resetStockInfo()}))}}},{key:"resetStockInfo",value:function(){this.$element.find(".variable-item").removeClass("wvs-show-stock-left-info"),this.$element.find(".wvs-stock-left-info").attr("data-wvs-stock-info","")}},{key:"getChosenAttributes",value:function(){var t={},e=0,a=0;return this.attributeFields.each((function(){var i=c(this).data("attribute_name")||c(this).attr("name"),n=c(this).val()||"";n.length>0&&a++,e++,t[i]=n})),{count:e,chosenCount:a,mayChosenCount:a+1,data:t}}},{key:"findStockVariations",value:function(t,e){for(var a=[],n=0,o=Object.entries(e.data);n<o.length;n++){var l=s(o[n],2),c=l[0];if(0===l[1].length){var u,d=i(this.$element.find("ul[data-attribute_name='".concat(c,"']")).data("attribute_values")||[]);try{for(d.s();!(u=d.n()).done;){var v=u.value,f=_.extend(e.data,r({},c,v)),h=this.findMatchingVariations(t,f);if(h.length>0){var p=h.shift(),m={};m.attribute_name=c,m.attribute_value=v,m.variation=p,a.push(m)}}}catch(t){d.e(t)}finally{d.f()}}}return a}},{key:"findMatchingVariations",value:function(t,e){for(var a=[],i=0;i<t.length;i++){var n=t[i];this.isMatch(n.attributes,e)&&a.push(n)}return a}},{key:"findMatchingVariations2",value:function(t,e){return t.filter((function(t){return Object.entries(e).every((function(e){var a=s(e,2),i=a[0],n=a[1],o=t.attributes[i];return!(void 0!==o&&void 0!==n&&0!==o.length&&0!==n.length&&o!==n)}))}))}},{key:"isMatch",value:function(t,e){var a=!0;for(var i in t)if(t.hasOwnProperty(i)){var n=t[i],o=e[i];void 0!==n&&void 0!==o&&0!==n.length&&0!==o.length&&n!==o&&(a=!1)}return a}},{key:"destroy",value:function(){this.$element.removeClass("wvs-loaded"),this.$element.removeData(this.name)}}])&&o(u.prototype,d),v&&o(u,v),Object.defineProperty(u,"prototype",{writable:!1}),t}()),d=function(e){return function(a,i){e.fn[a]=function(n){for(var o=this,r=arguments.length,s=new Array(r>1?r-1:0),l=1;l<r;l++)s[l-1]=arguments[l];return this.each((function(r,l){var c=e(l),u=c.data(a);if(u||(u=new i(c,e.extend({},n),a),c.data(a,u)),"string"==typeof n){if("object"===t(u[n]))return u[n];var d;if("function"==typeof u[n])return(d=u)[n].apply(d,s)}return o}))},e.fn[a].Constructor=i,e[a]=function(t){for(var i,n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];return(i=e({}))[a].apply(i,[t].concat(o))},e.fn[a].noConflict=function(){return e.fn[a]}}}(jQuery);d("WooVariationSwatches",u)}(window)}(),jQuery((function(t){try{t(document).on("woo_variation_swatches_init",(function(){t(".variations_form:not(.wvs-loaded)").WooVariationSwatches(),t(".woo_variation_swatches_variations_form:not(.wvs-loaded)").WooVariationSwatches(),t(".ywcp_inner_selected_container:not(.wvs-loaded)").WooVariationSwatches()}))}catch(t){window.console.log("Variation Swatches:",t)}t(document).on("wc_variation_form.wvs",(function(e){t(document).trigger("woo_variation_swatches_init")})),t(document).ajaxComplete((function(e,a,i){_.delay((function(){t(".variations_form:not(.wvs-loaded)").each((function(){t(this).wc_variation_form()}))}),1e3)})),t(document.body).on("wc-composite-initializing",".composite_data",(function(e,a){a.actions.add_action("component_options_state_changed",(function(e){t(e.$component_content).find(".variations_form").WooVariationSwatches("destroy")}))}))}));