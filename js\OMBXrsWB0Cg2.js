(function(e){var s=function(t){var n=Number(t.find(".pxl-counter__value").attr("data-delay"))+300||300;setTimeout(function(){elementorFrontend.waypoint(t.find(".pxl-counter__value:not(.effect-odometer)"),function(){var n=e(this),t=n.data(),s=String(t.toValue||"").match(/\.(\d+)/);s&&(t.rounding=s[1].length),typeof e.fn.numerator=="function"&&n.numerator(t)},{offset:"95%",triggerOnce:!0}),elementorFrontend.waypoint(t.find(".pxl-counter__value.effect-odometer"),function(){var n=e(this),t=n.data(),s=n[0],i=t.startnumber,a=t.endnumber,r=t.delimiter,c=t.duration,l=t.delay,o=new Odometer({el:s,value:i,format:r,theme:"default",duration:c});o.update(a),s._odometer=o},{offset:"95%",triggerOnce:!0})},n)};function t(e){return Number((e.text()||"0").replace(/[^\d.]/g,""))||0}function n(n){var o,a,r,i=Number(n.val()),c=n.closest(".e-con-inner"),s=c.find(".pxl-counter__value").first();if(!s.length)return;a=Number(s.attr("data-duration"))||2e3,r=s.attr("data-delimiter")||",",s.attr("data-endnumber",i).attr("data-to-value",i),s.hasClass("effect-odometer")?(o=s[0],o._odometer?(o._odometer.options.duration=a,o._odometer.options.format=r):o._odometer=new Odometer({el:o,value:t(s),format:r,theme:"default",duration:a}),o._odometer.update(i)):typeof e.fn.numerator=="function"?s.numerator({fromValue:t(s),toValue:i,duration:a,delimiter:r}):s.text(i.toLocaleString())}function o(t){t.find("select.pxl-select-change-counter__select").each(function(){var t=e(this);t.off(".pxlChange").on("change.pxlChange",function(){n(t)})})}e(document).on("change","select.pxl-select-change-counter__select",function(){n(e(this))}),e(window).on("elementor/frontend/init",function(){elementorFrontend.hooks.addAction("frontend/element_ready/pxl_counter.default",s),elementorFrontend.hooks.addAction("frontend/element_ready/pxl_select_change_counter_value.default",o)})})(jQuery)