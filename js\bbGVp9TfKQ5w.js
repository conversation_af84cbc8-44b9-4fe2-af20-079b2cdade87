(function(e){"use strict";if(typeof gsap=="undefined"||typeof ScrollTrigger=="undefined")return;gsap.registerPlugin(ScrollTrigger),window.__pxlZoomSTs=window.__pxlZoomSTs||[];function i(){if(!window.__pxlZoomSTs.length)return;window.__pxlZoomSTs.forEach(e=>{try{e.kill(!0)}catch{}}),window.__pxlZoomSTs=[]}function t(e,t=0){if(e==null||e==="")return t;const n=parseFloat(String(e).replace(/[^-\d.]/g,""));return Number.isFinite(n)?n:t}function a(e,n=0){return t(e,n)}function r(e){const n=e.dataset.zoomScale;if(n!=null&&n!=="")return t(n,.15);const s=e.dataset.zoomStart;return s!=null&&s!==""?t(s,15)/100:.15}function s(e,n,s){const o=e.dataset[n],i=o!=null&&o!==""?t(o,s):s;return`top ${i}%`}function o(e){const t=e.getAttribute("data-settings");if(!t)return null;try{const e=JSON.parse(t);return e&&(e._animation||e.animation)?e._animation||e.animation:null}catch{return null}}function c(e){const t=o(e);if(t)return t;const n=Array.from(e.classList).find(e=>e.startsWith("e-animations-"));return n?n.replace("e-animations-",""):null}function l(e,t){if(e.classList.remove("elementor-invisible"),!t)return;e.classList.contains("e-animated")||e.classList.add("e-animated");const n=`e-animations-${t}`;e.classList.contains(n)||e.classList.add(n)}function n(){i();const e=window.matchMedia&&window.matchMedia("(prefers-reduced-motion: reduce)").matches;document.querySelectorAll(".scroll-item-zoom").forEach(n=>{const v=t(n.dataset.zoomMin,0),y=t(n.dataset.zoomMax,1/0),m=window.innerWidth;if(m<v&&m>y)return;if(e&&n.dataset.zoomIgnorePrm!=="1")return;const p=r(n),g=a(n.dataset.zoomTop,20),h=s(n,"zoomStartScreen",80),u=s(n,"zoomEndScreen",0),b=!!n.dataset.zoomDebug,j=n.dataset.zoomOrigin&&n.dataset.zoomOrigin.trim()?n.dataset.zoomOrigin.trim():"top center";let i=n;if(n.dataset.zoomTrigger){const e=n.closest(n.dataset.zoomTrigger)||n.querySelector(n.dataset.zoomTrigger);e&&(i=e)}n.style.transformOrigin=j,n.style.willChange="transform";const d=gsap.fromTo(n,{scale:p,y:g},{scale:1,y:0,ease:"none",scrollTrigger:{trigger:i,start:h,end:u,scrub:0,invalidateOnRefresh:!0,markers:b}});d&&d.scrollTrigger&&window.__pxlZoomSTs.push(d.scrollTrigger);const _=Array.from(n.querySelectorAll(":scope .elementor-element")).filter(e=>e!==n&&!!(o(e)||Array.from(e.classList).some(e=>e.startsWith("e-animations-"))||e.classList.contains("elementor-invisible")));let f=!1;const w=ScrollTrigger.create({trigger:i,start:h,end:u,onUpdate(e){!f&&e.progress>=.99&&(f=!0,_.forEach(e=>{if(e.dataset.pxlPlayed==="1")return;e.dataset.pxlPlayed="1",l(e,c(e))}))}});window.__pxlZoomSTs.push(w)}),ScrollTrigger.refresh()}document.readyState==="complete"?n():e(window).on("load",n),e(window).on("resize",function(){clearTimeout(window.__pxlZoomResizeTimeout),window.__pxlZoomResizeTimeout=setTimeout(n,200)})})(jQuery)