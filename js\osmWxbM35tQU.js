/*!
 * ScrollTrigger 3.10.5
 * https://greensock.com
 * 
 * @license Copyright 2022, GreenSock. All rights reserved.
 * Subject to the terms at https://greensock.com/standard-license or for Club GreenSock members, the agreement issued with that membership.
 * @author: <PERSON>, <EMAIL>
 */!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).window=e.window||{})}(this,function(e){"use strict";function Nt(e,t){for(var n,s=0;s<t.length;s++)n=t[s],n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}function Qt(){return _||"undefined"!=typeof window&&(_=window.gsap)&&_.registerPlugin&&_}function H(e,t){return~F.indexOf(e)&&F[F.indexOf(e)+1][t]}function xe(e){return!!~Lt.indexOf(e)}function w(e,t,n,s,o){return e.addEventListener(t,n,{passive:!s,capture:!!o})}function g(e,t,n,s){return e.removeEventListener(t,n,!!s)}function It(){return N&&N.isPressed||i.cache++}function ze(e,t){function n(s){if(s||0===s){Ut&&(x.history.scrollRestoration="manual");var o=N&&N.isPressed;s=n.v=Math.round(s)||(N&&N.iOS?1:0),e(s),n.cacheID=i.cache,o&&rt("ss",s)}else(t||i.cache!==n.cacheID||rt("ref"))&&(n.cacheID=i.cache,n.v=e());return n.v+n.offset}return n.offset=0,e&&n}function O(e){return _.utils.toArray(e)[0]||("string"==typeof e&&!1!==_.config().nullTargetWarn?console.warn("Element not found:",e):null)}function I(e,t){var s=t.s,o=t.sc,n=i.indexOf(e),a=o===h.sc?1:2;return~n||(n=i.push(e)-1),i[n+a]||(i[n+a]=ze(H(e,s),!0)||(xe(e)?o:ze(function(t){return arguments.length?e[s]=t:e[s]})))}function Ge(e,t,n){function r(e,t){var r=pe();t||c<r-o?(a=s,s=e,i=o,o=r):n?s+=e:s=a+(e-a)/(r-i)*(o-i)}var s=e,a=e,o=pe(),i=o,c=t||50,l=Math.max(500,3*c);return{update:r,reset:function(){a=s=n?0:s,i=o=0},getVelocity:function(t){var u=i,c=a,d=pe();return!t&&0!==t||t===s||r(t),o===i||l<d-i?0:(s+(n?c:-c))/((n?d:o)-u)*1e3}}}function le(e,t){return t&&!e._gsapAllow&&e.preventDefault(),e.changedTouches?e.changedTouches[0]:e}function Mt(e){var t=Math.max.apply(Math,e),n=Math.min.apply(Math,e);return Math.abs(t)>=Math.abs(n)?t:n}function Ct(){(qe=_.core.globals().ScrollTrigger)&&qe.core&&function(){var t=qe.core,o=t.bridge||{},n=t._scrollers,s=t._proxies;n.push.apply(n,i),s.push.apply(s,F),i=n,F=s,rt=function(t,n){return o[t](n)}}()}function xt(e){return(_=e||Qt())&&"undefined"!=typeof document&&document.body&&(x=window,K=(oe=document).documentElement,X=oe.body,Lt=[x,oe,K,X],_.utils.clamp,V="onpointerenter"in X?"pointer":"mouse",yt=d.isTouch=x.matchMedia&&x.matchMedia("(hover: none), (pointer: coarse)").matches?1:"ontouchstart"in x||0<navigator.maxTouchPoints||0<navigator.msMaxTouchPoints?2:0,M=d.eventTypes=("ontouchstart"in K?"touchstart,touchmove,touchcancel,touchend":"onpointerdown"in K?"pointerdown,pointermove,pointercancel,pointerup":"mousedown,mousemove,mouseup,mouseup").split(","),setTimeout(function(){return Ut=0},500),Ct(),at=1),at}var _,at,x,oe,K,X,yt,V,qe,Lt,M,N,Ut=1,se=[],i=[],F=[],pe=Date.now,rt=function(t,n){return n},ye="scrollLeft",_e="scrollTop",b={s:ye,p:"left",p2:"Left",os:"right",os2:"Right",d:"width",d2:"Width",a:"x",sc:ze(function(e){return arguments.length?x.scrollTo(e,h.sc()):x.pageXOffset||oe[ye]||K[ye]||X[ye]||0})},h={s:_e,p:"top",p2:"Top",os:"bottom",os2:"Bottom",d:"height",d2:"Height",a:"y",op:b,sc:ze(function(e){return arguments.length?x.scrollTo(b.sc(),e):x.pageYOffset||oe[_e]||K[_e]||X[_e]||0})};b.op=h,i.cache=0,d=(Je.prototype.init=function(t){at||xt(_)||console.warn("Please gsap.registerPlugin(Observer)"),qe||Ct();var C=t.tolerance,k=t.dragMinimum,a=t.type,s=t.target,U=t.lineHeight,S=t.debounce,i=t.preventDefault,m=t.onStop,Be=t.onStopDelay,f=t.ignore,R=t.wheelSpeed,Ve=t.event,we=t.onDragStart,ae=t.onDragEnd,J=t.onDrag,Ee=t.onPress,Ce=t.onRelease,te=t.onRight,Me=t.onLeft,Re=t.onUp,He=t.onDown,Pe=t.onChangeX,De=t.onChangeY,ze=t.onChange,Te=t.onToggleX,Fe=t.onToggleY,Se=t.onHover,Ae=t.onHoverEnd,H=t.onMove,ke=t.ignoreCheck,d=t.isNormalizer,G=t.onGestureStart,he=t.onGestureEnd,ue=t.onWheel,re=t.onEnable,Z=t.onDisable,_e=t.onClick,y=t.scrollSpeed,u=t.capture,ee=t.allowClicks,$e=t.lockAxis,ne=t.onLockAxis;function ie(){return Oe=pe()}function l(e,t){return(n.event=e)&&f&&~f.indexOf(e.target)||t&&Ie&&"touch"!==e.pointerType||ke&&ke(e,t)}function E(){var e=n.deltaX=Mt(r),t=n.deltaY=Mt(c),s=Math.abs(e)>=C,o=Math.abs(t)>=C;ze&&(s||o)&&ze(n,e,t,r,c),s&&(te&&0<n.deltaX&&te(n),Me&&n.deltaX<0&&Me(n),Pe&&Pe(n),Te&&n.deltaX<0!=L<0&&Te(n),L=n.deltaX,r[0]=r[1]=r[2]=0),o&&(He&&0<n.deltaY&&He(n),Re&&n.deltaY<0&&Re(n),De&&De(n),Fe&&n.deltaY<0!=z<0&&Fe(n),z=n.deltaY,c[0]=c[1]=c[2]=0),($||T)&&(H&&H(n),ne&&P&&ne(n),T&&(J(n),T=!1),$=P=!1),B&&(ue(n),B=!1),j=0}function ce(e,t,s){r[s]+=e,c[s]+=t,n._vx.update(e),n._vy.update(t),S?j=j||requestAnimationFrame(E):E()}function de(e,t){"y"!==p&&(r[2]+=e,n._vx.update(e,!0)),"x"!==p&&(c[2]+=t,n._vy.update(t,!0)),$e&&!p&&(n.axis=p=Math.abs(e)>Math.abs(t)?"x":"y",P=!0),S?j=j||requestAnimationFrame(E):E()}function q(e){if(!l(e,1)){var t=(e=le(e,i)).clientX,s=e.clientY,a=t-n.x,r=s-n.y,o=n.isDragging;n.x=t,n.y=s,(o||Math.abs(n.startX-t)>=k||Math.abs(n.startY-s)>=k)&&(J&&(T=!0),o||(n.isDragging=!0),de(a,r),o||we&&we(n))}}function A(e){if(!l(e,1)){g(d?s:o,M[1],q,!0);var a=n.isDragging&&(3<Math.abs(n.x-n.startX)||3<Math.abs(n.y-n.startY)),t=le(e);a||(n._vx.reset(),n._vy.reset(),i&&ee&&_.delayedCall(.08,function(){if(300<pe()-Oe&&!e.defaultPrevented)if(e.target.click)e.target.click();else if(o.createEvent){var n=o.createEvent("MouseEvents");n.initMouseEvent("click",!0,!0,x,1,t.screenX,t.screenY,t.clientX,t.clientY,!1,!1,!1,!1,0,null),e.target.dispatchEvent(n)}})),n.isDragging=n.isGesturing=n.isPressed=!1,m&&!d&&v.restart(!0),ae&&a&&ae(n),Ce&&Ce(n,a)}}function me(e){return e.touches&&1<e.touches.length&&(n.isGesturing=!0)&&G(e,n.isDragging)}function fe(){return(n.isGesturing=!1)||he(n)}function ge(e){if(!l(e)){var t=D(),n=W();ce((t-Ne)*y,(n-Le)*y,1),Ne=t,Le=n,m&&v.restart(!0)}}function ve(e){if(!l(e)){e=le(e,i),ue&&(B=!0);var t=(1===e.deltaMode?U:2===e.deltaMode?x.innerHeight:1)*R;ce(e.deltaX*t,e.deltaY*t,0),m&&!d&&v.restart(!0)}}function be(e){if(!l(e)){var t=e.clientX,s=e.clientY,o=t-n.x,i=s-n.y;n.x=t,n.y=s,$=!0,(o||i)&&de(o,i)}}function je(e){n.event=e,Se(n)}function ye(e){n.event=e,Ae(n)}function Q(e){return l(e)||le(e,i)&&_e(n)}this.target=s=O(s)||K,this.vars=t;var p,j,T,P,B,$,f=f&&_.utils.toArray(f),C=C||0,k=k||0,R=R||1,y=y||1,a=a||"wheel,touch,pointer",S=!1!==S,U=U||parseFloat(x.getComputedStyle(X).lineHeight)||22,n=this,L=0,z=0,D=I(s,b),W=I(s,h),Ne=D(),Le=W(),Ie=~a.indexOf("touch")&&!~a.indexOf("pointer")&&"pointerdown"===M[0],F=xe(s),o=s.ownerDocument||oe,r=[0,0,0],c=[0,0,0],Oe=0,Y=n.onPress=function(e){l(e,1)||(n.axis=p=null,v.pause(),n.isPressed=!0,e=le(e),L=z=0,n.startX=n.x=e.clientX,n.startY=n.y=e.clientY,n._vx.reset(),n._vy.reset(),w(d?s:o,M[1],q,i,!0),n.deltaX=n.deltaY=0,Ee&&Ee(n))},v=n._dc=_.delayedCall(Be||.25,function(){n._vx.reset(),n._vy.reset(),v.pause(),m&&m(n)}).pause();n.deltaX=n.deltaY=0,n._vx=Ge(0,50,!0),n._vy=Ge(0,50,!0),n.scrollX=D,n.scrollY=W,n.isDragging=n.isGesturing=n.isPressed=!1,n.enable=function(e){return n.isEnabled||(w(F?o:s,"scroll",It),0<=a.indexOf("scroll")&&w(F?o:s,"scroll",ge,i,u),0<=a.indexOf("wheel")&&w(s,"wheel",ve,i,u),(0<=a.indexOf("touch")&&yt||0<=a.indexOf("pointer"))&&(w(s,M[0],Y,i,u),w(o,M[2],A),w(o,M[3],A),ee&&w(s,"click",ie,!1,!0),_e&&w(s,"click",Q),G&&w(o,"gesturestart",me),he&&w(o,"gestureend",fe),Se&&w(s,V+"enter",je),Ae&&w(s,V+"leave",ye),H&&w(s,V+"move",be)),n.isEnabled=!0,e&&e.type&&Y(e),re&&re(n)),n},n.disable=function(){n.isEnabled&&(se.filter(function(e){return e!==n&&xe(e.target)}).length||g(F?o:s,"scroll",It),n.isPressed&&(n._vx.reset(),n._vy.reset(),g(d?s:o,M[1],q,!0)),g(F?o:s,"scroll",ge,u),g(s,"wheel",ve,u),g(s,M[0],Y,u),g(o,M[2],A),g(o,M[3],A),g(s,"click",ie,!0),g(s,"click",Q),g(o,"gesturestart",me),g(o,"gestureend",fe),g(s,V+"enter",je),g(s,V+"leave",ye),g(s,V+"move",be),n.isEnabled=n.isPressed=n.isDragging=!1,Z&&Z(n))},n.kill=function(){n.disable();var e=se.indexOf(n);0<=e&&se.splice(e,1),N===n&&(N=0)},se.push(n),d&&xe(s)&&(N=n),n.enable(Ve)},function(t,n,s){return n&&Nt(t.prototype,n),s&&Nt(t,s),t}(Je,[{key:"velocityX",get:function(){return this._vx.getVelocity()}},{key:"velocityY",get:function(){return this._vy.getVelocity()}}]),Je);function Je(e){this.init(e)}d.version="3.10.5",d.create=function(e){return new d(e)},d.register=xt,d.getAll=function(){return se.slice()},d.getById=function(e){return se.filter(function(t){return t.vars.id===e})[0]},Qt()&&_.registerPlugin(d);function Ht(){return Ie=1}function vt(){return Ie=0}function R(e){return e}function ie(e){return Math.round(1e5*e)/1e5||0}function St(){return"undefined"!=typeof window}function Dt(){return t||St()&&(t=window.gsap)&&t.registerPlugin&&t}function G(e){return!!~Gt.indexOf(e)}function Pt(e){return H(e,"getBoundingClientRect")||(G(e)?function(){return Ce.width=n.innerWidth,Ce.height=n.innerHeight,Ce}:function(){return D(e)})}function P(e,t){var s=t.s,o=t.d2,a=t.d,i=t.a;return(s="scroll"+o)&&(i=H(e,s))?i()-Pt(e)()[a]:G(e)?(L[s]||r[s])-(n["inner"+o]||L["client"+o]||r["client"+o]):e[s]-e["offset"+o]}function Fe(e,t){for(var n=0;n<Q.length;n+=3)t&&!~t.indexOf(Q[n+1])||e(Q[n],Q[n+1],Q[n+2])}function T(e){return"string"==typeof e}function S(e){return"function"==typeof e}function ae(e){return"number"==typeof e}function Se(e){return"object"==typeof e}function Ae(e){return S(e)&&e()}function Vt(e,t){return function(){var n=Ae(e),s=Ae(t);return function(){Ae(n),Ae(s)}}}function ge(e,t,n){return e&&e.progress(t?0:1)&&n&&e.pause()}function lt(e,t){if(e.enabled){var n=t(e);n&&n.totalTime&&(e.callbackAnimation=n)}}function z(e){return n.getComputedStyle(e)}function Tt(e,t){for(var n in t)n in e||(e[n]=t[n]);return e}function it(e,t){var n=t.d2;return e["offset"+n]||e["client"+n]||0}function qt(e){var t,n=[],s=e.labels,o=e.duration();for(t in s)n.push(s[t]/o);return n}function Ze(e){var s=t.utils.snap(e),n=Array.isArray(e)&&e.slice(0).sort(function(e,t){return e-t});return n?function(e,t,o){var i;if(void 0===o&&(o=.001),!t)return s(e);if(0<t){for(e-=o,i=0;i<n.length;i++)if(n[i]>=e)return n[i];return n[i-1]}for(i=n.length,e+=o;i--;)if(n[i]<=e)return n[i];return n[0]}:function(t,n,o){void 0===o&&(o=.001);var i=s(t);return!n||Math.abs(i-t)<o||i-t<0==n<0?i:s(n<0?t-e:t+e)}}function Le(e,t,n,s){return n.split(",").forEach(function(n){return e(t,n,s)})}function f(e,t,n,s,o){return e.addEventListener(t,n,{passive:!s,capture:!!o})}function m(e,t,n,s){return e.removeEventListener(t,n,!!s)}function We(e,t,n){return n&&n.wheelHandler&&e(t,"wheel",n)}function Ue(e,t){if(T(e)){var n=e.indexOf("="),s=~n?(e.charAt(n-1)+1)*parseFloat(e.substr(n+1)):0;~n&&(e.indexOf("%")>n&&(s*=t/100),e=e.substr(0,n-1)),e=s+(e in Be?Be[e]*t:~e.indexOf("%")?parseFloat(e)*t/100:parseFloat(e)||0)}return e}function Ke(e,t,n,s,o,i,c,l){var b=o.startColor,j=o.endColor,y=o.fontSize,_=o.indent,w=o.fontWeight,d=a.createElement("div"),f=G(n)||"fixed"===H(n,"pinType"),g=-1!==e.indexOf("scroller"),u=f?r:n,p=-1!==e.indexOf("start"),v=p?b:j,m="border-color:"+v+";font-size:"+y+";color:"+v+";font-weight:"+w+";pointer-events:none;white-space:nowrap;font-family:sans-serif,Arial;z-index:1000;padding:4px 8px;border-width:0;border-style:solid;";return m+="position:"+((g||l)&&f?"fixed;":"absolute;"),!g&&!l&&f||(m+=(s===h?ht:ut)+":"+(i+parseFloat(_))+"px;"),c&&(m+="box-sizing:border-box;text-align:left;width:"+c.offsetWidth+"px;"),d._isStart=p,d.setAttribute("class","gsap-marker-"+e+(t?" marker-"+t:"")),d.style.cssText=m,d.innerText=t||0===t?e+"-"+t:e,u.children[0]?u.insertBefore(d,u.children[0]):u.appendChild(d),d._offset=d["offset"+s.op.d2],He(d,0,s,p),d}function jt(){return 34<j()-C&&ce()}function ne(){k&&k.isPressed&&!(k.startX>r.clientWidth)||(i.cache++,tt=tt||requestAnimationFrame(ce),C||W("scrollStart"),C=j())}function _t(){$t=n.innerWidth,Jt=n.innerHeight}function be(){i.cache++,p||pt||a.fullscreenElement||a.webkitFullscreenElement||Wt&&$t===n.innerWidth&&!(Math.abs(n.innerHeight-Jt)>.25*n.innerHeight)||De.restart(!0)}function At(e){var o,a=t.ticker.frame,i=[],s=0;if(Ft!==a||ve){for(Te();s<c.length;s+=4)(o=n.matchMedia(c[s]).matches)!==c[s+3]&&((c[s+3]=o)?i.push(s):Te(1,c[s])||S(c[s+2])&&c[s+2]());for(mt(),s=0;s<i.length;s++)o=i[s],Z=c[o],c[o+2]=c[o+1](e);Z=0,U&&J(0,1),Ft=a,W("matchMedia")}}function nt(){return m(o,"scrollEnd",nt)||J(!0)}function kt(){return i.cache++&&i.forEach(function(e){return"function"==typeof e&&(e.rec=0)})}function Qe(e,t,n,s){if(!e._gsap.swappedIn){for(var a,r=Xe.length,i=t.style,o=e.style;r--;)i[a=Xe[r]]=n[a];i.position="absolute"===n.position?"absolute":"relative","inline"===n.display&&(i.display="inline-block"),o[ut]=o[ht]=i.flexBasis="auto",i.overflow="visible",i.boxSizing="border-box",i[Y]=it(e,b)+v,i[q]=it(e,h)+v,i[u]=o[A]=o.top=o.left="0",re(s),o[Y]=o.maxWidth=n[Y],o[q]=o.maxHeight=n[q],o[u]=n[u],e.parentNode!==t&&(e.parentNode.insertBefore(t,e),t.appendChild(e)),e._gsap.swappedIn=!0}}function Oe(e){for(var s=Ee.length,o=e.style,n=[],t=0;t<s;t++)n.push(Ee[t],o[Ee[t]]);return n.t=e,n}function Xt(e,t,n,s,o,i,a,c,l,d,u,h,m){if(S(e)&&(e=e(c)),T(e)&&"max"===e.substr(0,3)&&(e=h+("="===e.charAt(4)?Ue("0"+e.substr(3),n):0)),x=m?m.time():0,m&&m.seek(0),ae(e)?a&&He(a,n,s,!0):(S(t)&&(t=t(c)),y=e.split(" "),f=O(t)||r,(g=D(f)||{})&&(g.left||g.top)||"none"!==z(f).display||(j=f.style.display,f.style.display="block",g=D(f),j?f.style.display=j:f.style.removeProperty("display")),C=Ue(y[0],g[s.d]),b=Ue(y[1]||"0",n),e=g[s.p]-l[s.p]-d+C+o-b,a&&He(a,b,s,n-b<20||a._isStart&&20<b),n-=n-b),i){var f,p,g,b,j,y,w,x,C,_=e+n,E=i._isStart;p="scroll"+s.d2,He(i,_,s,E&&20<_||!E&&(u?Math.max(r[p],L[p]):i.parentNode[p])<=_+1),u&&(l=D(a),u&&(i.style[s.op.p]=l[s.op.p]-s.op.m-i._offset+v))}return m&&f&&(p=D(f),m.seek(h),w=D(f),m._caScrollDist=p[s.p]-w[s.p],e=e/m._caScrollDist*h),m&&m.seek(x),m?e:Math.round(e)}function Yt(e,n,s,o){if(e.parentNode!==n){var i,c,a=e.style;if(n===r){for(i in e._stOrig=a.cssText,c=z(e))+i||tn.test(i)||!c[i]||"string"!=typeof a[i]||"0"===i||(a[i]=c[i]);a.top=s,a.left=o}else a.cssText=e._stOrig;t.core.getCache(e).uncache=1,n.appendChild(e)}}function Rt(e,n){function s(n,c,l,d,u){var h=s.tween,m=c.onComplete,f={};return l=l||i(),u=d&&u||0,d=d||n-l,h&&h.kill(),o=Math.round(l),c[r]=n,(c.modifiers=f)[r]=function(e){return(e=Math.round(i()))!==o&&e!==a&&3<Math.abs(e-o)&&3<Math.abs(e-a)?(h.kill(),s.tween=0):e=l+d*h.ratio+u*h.ratio*h.ratio,a=o,o=Math.round(e)},c.onComplete=function(){s.tween=0,m&&m.call(h)},h=s.tween=t.to(e,c)}var o,a,i=I(e,n),r="_scroll"+n.p2;return(e[r]=i).wheelHandler=function(){return s.tween&&s.tween.kill()&&(s.tween=0)},f(e,"wheel",i.wheelHandler),s}var t,n,a,r,d,L,U,Gt,De,Ne,ee,Re,Pe,p,Ie,ot,y,wt,bt,Q,gt,dt,pt,k,Wt,Jt,$t,B,et,tt,Z,Ft,je,$,ve=1,j=Date.now,ct=j(),C=0,Ye=0,te=Math.abs,ht="right",ut="bottom",Y="width",q="height",fe="Right",me="Left",he="Top",ue="Bottom",u="padding",A="margin",$e="Width",rn="Height",v="px",D=function(n,s){var o=s&&"matrix(1, 0, 0, 1, 0, 0)"!==z(n)[ot]&&t.to(n,{x:0,y:0,xPercent:0,yPercent:0,rotation:0,rotationX:0,rotationY:0,scale:1,skewX:0,skewY:0}).progress(1),i=n.getBoundingClientRect();return o&&o.progress(0).kill(),i},Et={startColor:"green",endColor:"red",indent:0,fontSize:"16px",fontWeight:"normal"},Ve={toggleActions:"play",anticipatePin:0},Be={top:0,left:0,center:.5,bottom:1,right:1},He=function(n,s,o,i){var a={display:"block"},r=o[i?"os2":"p2"],c=o[i?"p2":"os2"];n._isFlipped=i,a[o.a+"Percent"]=i?-100:0,a[o.a]=i?"1px":0,a["border"+r+$e]=1,a["border"+c+$e]=0,a[o.p]=s+"px",t.set(n,a)},s=[],st={},de={},on=[],c=[],W=function(t){return de[t]&&de[t].map(function(e){return e()})||on},E=[],mt=function(t){for(var n=0;n<E.length;n+=5)t&&E[n+4]!==t||(E[n].style.cssText=E[n+1],E[n].getBBox&&E[n].setAttribute("transform",E[n+2]||""),E[n+3].uncache=1)},Te=function(t,n){var o;for(y=0;y<s.length;y++)o=s[y],n&&o.media!==n||(t?o.kill(1):o.revert());n&&mt(n),n||W("revert")},Me=0,J=function(t,n){if(!C||t){je=!0;var i=W("refreshInit");gt&&o.sort(),n||Te(),s.slice(0).forEach(function(e){return e.refresh()}),s.forEach(function(e){return"max"===e.vars.end&&e.setPositions(e.start,P(e.scroller,e._dir))}),i.forEach(function(e){return e&&e.render&&e.render(-1)}),kt(),De.pause(),Me++,je=!1,W("refresh")}else f(o,"scrollEnd",nt)},Bt=0,ke=1,ce=function(){if(!je){o.isUpdating=!0,$&&$.update(0);var t=s.length,n=j(),i=50<=n-ct,a=t&&s[0].scroll();if(ke=a<Bt?-1:1,Bt=a,i&&(C&&!Ie&&200<n-C&&(C=0,W("scrollEnd")),Re=ct,ct=n),ke<0){for(y=t;0<y--;)s[y]&&s[y].update(0,i);ke=1}else for(y=0;y<t;y++)s[y]&&s[y].update(0,i);o.isUpdating=!1}tt=0},Xe=["left","top",ut,ht,A+ue,A+fe,A+he,A+me,"display","flexShrink","float","zIndex","gridColumnStart","gridColumnEnd","gridRowStart","gridRowEnd","gridArea","justifySelf","alignSelf","placeSelf","order"],Ee=Xe.concat([Y,q,"boxSizing","max"+$e,"max"+rn,"position",A,u,u+he,u+fe,u+ue,u+me]),sn=/([A-Z])/g,re=function(n){if(n){var s,i,a=n.t.style,r=n.length,o=0;for((n.t._gsap||t.core.getCache(n.t)).uncache=1;o<r;o+=2)i=n[o+1],s=n[o],i?a[s]=i:a[s]&&a.removeProperty(s.replace(sn,"-$1").toLowerCase())}},Ce={left:0,top:0},tn=/(webkit|moz|length|cssText|inset)/i,o=(l.prototype.init=function(o,i){if(this.progress=this.start=0,this.vars&&this.kill(!0,!0),Ye){var V,U,Lt,xe,Le,x,W,le,Te,ce,we,ze,M,Xe,tt,rt,X,At,kt,ct,Pe,pe,Mt,at,Dt,yt,bt,Ge,ft,E,wt,_e,ye,Fe,De,ot,_t,He,ht,ut,Bt,Me=(o=Tt(T(o)||ae(o)||o.nodeType?{trigger:o}:o,Ve)).onUpdate,Ee=o.toggleClass,K=o.id,zt=o.onToggle,Nt=o.onRefresh,Je=o.scrub,B=o.trigger,d=o.pin,J=o.pinSpacing,Wt=o.invalidateOnRefresh,Ct=o.anticipatePin,It=o.onScrubComplete,Ht=o.onSnapComplete,mt=o.once,k=o.snap,pt=o.pinReparent,se=o.pinSpacer,_=o.containerAnimation,jt=o.fastScrollEnd,qe=o.preventOverlaps,g=o.horizontal||o.containerAnimation&&!1!==o.horizontal?b:h,Q=!Je&&0!==Je,w=O(o.scroller||n),xt=t.core.getCache(w),oe=G(w),de="fixed"===("pinType"in o?o.pinType:H(w,"pinType")||oe&&"fixed"),We=[o.onEnter,o.onLeave,o.onEnterBack,o.onLeaveBack],St=Q&&o.toggleActions.split(" "),Ot="markers"in o?o.markers:Ve.markers,Ft=oe?0:parseFloat(z(w)["border"+g.p2+$e])||0,c=this,Ae=o.onRefreshInit&&function(){return o.onRefreshInit(c)},Vt=function(t,s,o){var r=o.d,i=o.d2,a=o.a;return(a=H(t,"getBoundingClientRect"))?function(){return a()[r]}:function(){return(s?n["inner"+i]:t["client"+i])||0}}(w,oe,g),$t=function(t,n){return!n||~F.indexOf(t)?Pt(t):function(){return Ce}}(w,oe),Be=0,vt=0,N=I(w,g);(c.media=Z,c._dir=g,Ct*=45,c.scroller=w,c.scroll=_?_.time.bind(_):N,xe=N(),c.vars=o,i=i||o.animation,"refreshPriority"in o&&(gt=1,-9999===o.refreshPriority&&($=c)),xt.tweenScroll=xt.tweenScroll||{top:Rt(w,h),left:Rt(w,b)},c.tweenTo=V=xt.tweenScroll[g.p],c.scrubDuration=function(e){(wt=ae(e)&&e)?E?E.duration(e):E=t.to(i,{ease:"expo",totalProgress:"+=0.001",duration:wt,paused:!0,onComplete:function(){return It&&It(c)}}):(E&&E.progress(1).kill(),E=0)},i&&(i.vars.lazy=!1,i._initted||!1!==i.vars.immediateRender&&!1!==o.immediateRender&&i.render(0,!0,!0),c.animation=i.pause(),(i.scrollTrigger=c).scrubDuration(Je),Ge=0,K=K||i.vars.id),s.push(c),k&&(Se(k)&&!k.push||(k={snapTo:k}),"scrollBehavior"in r.style&&t.set(oe?[r,L]:w,{scrollBehavior:"auto"}),Lt=S(k.snapTo)?k.snapTo:"labels"===k.snapTo?function(n){return function(e){return t.utils.snap(qt(n),e)}}(i):"labelsDirectional"===k.snapTo?function(t){return function(e,n){return Ze(qt(t))(e,n.direction)}}(i):!1!==k.directional?function(e,t){return Ze(k.snapTo)(e,j()-vt<500?0:t.direction)}:t.utils.snap(k.snapTo),_e=k.duration||{min:.1,max:2},_e=Se(_e)?ee(_e.min,_e.max):ee(_e,_e),ye=t.delayedCall(k.delay||wt/2||.1,function(){var e=N(),h=j()-vt<500,o=V.tween;if(!(h||Math.abs(c.getVelocity())<10)||o||Ie||Be===e)c.isActive&&Be!==e&&ye.restart(!0);else{var s=(e-x)/M,r=i&&!Q?i.totalProgress():s,l=h?0:(r-ft)/(j()-Re)*1e3||0,a=t.utils.clamp(-s,1-s,te(l/2)*l/.185),u=s+(!1===k.inertia?0:a),d=ee(0,1,Lt(u,c)),n=Math.round(x+d*M),m=k.onStart,f=k.onInterrupt,p=k.onComplete;if(e<=W&&x<=e&&n!==e){if(o&&!o._initted&&o.data<=te(n-e))return;!1===k.inertia&&(a=d-s),V(n,{duration:_e(te(.185*Math.max(te(u-r),te(d-r))/l/.05||0)),ease:k.ease||"power3",data:te(n-e),onInterrupt:function(){return ye.restart(!0)&&f&&f(c)},onComplete:function(){c.update(),Be=N(),Ge=ft=i&&!Q?i.totalProgress():c.progress,Ht&&Ht(c),p&&p(c)}},e,a*M,n-e-a*M),m&&m(c,V.tween)}}}).pause()),K&&(st[K]=c),He=(He=(B=c.trigger=O(B||d))&&B._gsap&&B._gsap.stRevert)&&He(c),d=!0===d?B:O(d),T(Ee)&&(Ee={targets:B,className:Ee}),d&&(!1===J||J===A||(J=!(!J&&"flex"===z(d.parentNode).display)&&u),c.pin=d,!1!==o.force3D&&t.set(d,{force3D:!0}),(U=t.core.getCache(d)).spacer?Xe=U.pinState:(se&&((se=O(se))&&!se.nodeType&&(se=se.current||se.nativeElement),U.spacerIsNative=!!se,se&&(U.spacerState=Oe(se))),U.spacer=X=se||a.createElement("div"),X.classList.add("pin-spacer"),K&&X.classList.add("pin-spacer-"+K),U.pinState=Xe=Oe(d)),c.spacer=X=U.spacer,bt=z(d),Mt=bt[J+g.os2],kt=t.getProperty(d),ct=t.quickSetter(d,g.a,v),Qe(d,X,bt),rt=Oe(d)),Ot)&&(ze=Se(Ot)?Tt(Ot,Et):Et,ce=Ke("scroller-start",K,w,g,ze,0),we=Ke("scroller-end",K,w,g,ze,0,ce),At=ce["offset"+g.op.d2],ht=O(H(w,"content")||w),le=this.markerStart=Ke("start",K,ht,g,ze,At,0,_),Te=this.markerEnd=Ke("end",K,ht,g,ze,At,0,_),_&&(_t=t.quickSetter([le,Te],g.a,v)),de||F.length&&!0===H(w,"fixedMarkers")||(function(t){var n=z(t).position;t.style.position="absolute"===n||"fixed"===n?n:"relative"}(oe?r:w),t.set([ce,we],{force3D:!0}),Dt=t.quickSetter(ce,g.a,v),yt=t.quickSetter(we,g.a,v))),_&&(ut=_.vars.onUpdate,Bt=_.vars.onUpdateParams,_.eventCallback("onUpdate",function(){c.update(0,0,1),ut&&ut.apply(Bt||[])})),c.previous=function(){return s[s.indexOf(c)-1]},c.next=function(){return s[s.indexOf(c)+1]},c.revert=function(e){var t=!1!==e||!c.enabled,n=p;t!==c.isReverted&&(t&&(!c.scroll.rec&&p&&je&&(c.scroll.rec=N()),De=Math.max(N(),c.scroll.rec||0),Fe=c.progress,ot=i&&i.progress()),le&&[le,Te,ce,we].forEach(function(e){return e.style.display=t?"none":"block"}),t&&(p=1),c.update(t),p=n,d&&(t?function(t,n,s){re(s);var o,i=t._gsap;i.spacerIsNative?re(i.spacerState):t._gsap.swappedIn&&(o=n.parentNode,o&&(o.insertBefore(t,n),o.removeChild(n))),t._gsap.swappedIn=!1}(d,X,Xe):pt&&c.isActive||Qe(d,X,z(d),at)),c.isReverted=t)},c.refresh=function(e,n){if(!p&&c.enabled||n)if(d&&e&&C)f(l,"scrollEnd",nt);else{!je&&Ae&&Ae(c),p=1,vt=j(),V.tween&&(V.tween.kill(),V.tween=0),E&&E.pause(),Wt&&i&&i.time(-.01,!0).invalidate(),c.isReverted||c.revert();for(var y,k,ee,a,m,$,G,ie,ve,K,te=Vt(),ge=$t(),Z=_?_.duration():P(w,g),H=0,U=0,F=o.end,ne=o.endTrigger||B,L=o.start||(0!==o.start&&B?d?"0 0":"0 100%":0),be=c.pinnedContainer=o.pinnedContainer&&O(o.pinnedContainer),se=B&&Math.max(0,s.indexOf(c))||0,R=se;R--;)(m=s[R]).end||m.refresh(0,1)||(p=1),!($=m.pin)||$!==B&&$!==d||m.isReverted||((K=K||[]).unshift(m),m.revert()),m!==s[R]&&(se--,R--);for(S(L)&&(L=L(c)),x=Xt(L,B,te,g,N(),le,ce,c,ge,Ft,de,Z,_)||(d?-.001:0),S(F)&&(F=F(c)),T(F)&&!F.indexOf("+=")&&(~F.indexOf(" ")?F=(T(L)?L.split(" ")[0]:"")+F:(H=Ue(F.substr(2),te),F=T(L)?L:x+H,ne=B)),W=Math.max(x,Xt(F||(ne?"100% 0":Z),ne,te,g,N()+H,Te,we,c,ge,Ft,de,Z,_))||-.001,M=W-x||(x-=.01)&&.001,H=0,R=se;R--;)($=(m=s[R]).pin)&&m.start-m._pinPush<x&&!_&&0<m.end&&(k=m.end-m.start,$!==B&&$!==be||ae(L)||(H+=k*(1-m.progress)),$===d&&(U+=k));if(x+=H,W+=H,c._pinPush=U,le&&H&&((k={})[g.a]="+="+H,be&&(k[g.p]="-="+N()),t.set([le,Te],k)),d)k=z(d),G=g===h,ee=N(),Pe=parseFloat(kt(g.a))+U,!Z&&1<W&&((oe?r:w).style["overflow-"+g.a]="scroll"),Qe(d,X,k),rt=Oe(d),y=D(d,!0),ie=de&&I(w,G?b:h)(),J&&((at=[J+g.os2,M+U+v]).t=X,(R=J===u?it(d,g)+M+U:0)&&at.push(g.d,R+v),re(at),de&&N(De)),de&&((a={top:y.top+(G?ee-x:ie)+v,left:y.left+(G?ie:ee-x)+v,boxSizing:"border-box",position:"fixed"})[Y]=a.maxWidth=Math.ceil(y.width)+v,a[q]=a.maxHeight=Math.ceil(y.height)+v,a[A]=a[A+he]=a[A+fe]=a[A+ue]=a[A+me]="0",a[u]=k[u],a[u+he]=k[u+he],a[u+fe]=k[u+fe],a[u+ue]=k[u+ue],a[u+me]=k[u+me],tt=function(t,n,s){for(var o,a=[],r=t.length,i=s?8:0;i<r;i+=2)o=t[i],a.push(o,o in n?n[o]:t[i+1]);return a.t=t.t,a}(Xe,a,pt)),i?(ve=i._initted,dt(1),i.render(i.duration(),!0,!0),pe=kt(g.a)-Pe+M+U,M!==pe&&de&&tt.splice(tt.length-2,2),i.render(0,!0,!0),ve||i.invalidate(),dt(0)):pe=M;else if(B&&N()&&!_)for(y=B.parentNode;y&&y!==r;)y._pinOffset&&(x-=y._pinOffset,W-=y._pinOffset),y=y.parentNode;K&&K.forEach(function(e){return e.revert(!1)}),c.start=x,c.end=W,xe=Le=N(),_||(xe<De&&N(De),c.scroll.rec=0),c.revert(!1),ye&&(Be=-1,c.isActive&&N(x+M*Fe),ye.restart(!0)),p=0,i&&Q&&(i._initted||ot)&&i.progress()!==ot&&i.progress(ot,!0).render(i.time(),!0,!0),Fe===c.progress&&!_||(i&&!Q&&i.totalProgress(Fe,!0),c.progress=Fe,c.update(0,0,1)),d&&J&&(X._pinOffset=Math.round(c.progress*pe)),Nt&&Nt(c)}},c.getVelocity=function(){return(N()-Le)/(j()-Re)*1e3||0},c.endAnimation=function(){ge(c.callbackAnimation),i&&(E?E.progress(1):i.paused()?Q||ge(i,c.direction<0,1):ge(i,i.reversed()))},c.labelToScroll=function(e){return i&&i.labels&&(x||c.refresh()||x)+i.labels[e]/i.duration()*M||0},c.getTrailing=function(e){var t=s.indexOf(c),n=0<c.direction?s.slice(0,t).reverse():s.slice(t+1);return(T(e)?n.filter(function(t){return t.vars.preventOverlaps===e}):n).filter(function(e){return 0<c.direction?e.end<=x:e.start>=W})},c.update=function(e,t,n){if(!_||n||e){var a,l,m,f,b,y,O,F,T,z,o=c.scroll(),A=e?0:(o-x)/M,s=A<0?0:1<A?1:A||0,u=c.progress;(t&&(Le=xe,xe=_?N():o,k&&(ft=Ge,Ge=i&&!Q?i.totalProgress():s)),Ct&&!s&&d&&!p&&!ve&&C&&x<o+(o-Le)/(j()-Re)*Ct&&(s=1e-4),s!==u&&c.enabled)&&((b=(m=(f=c.isActive=!!s&&s<1)!=(!!u&&u<1))||!!s!=!!u,c.direction=u<s?1:-1,c.progress=s,b&&!p&&(a=s&&!u?0:1===s?1:1===u?2:3,Q&&(l=!m&&"none"!==St[a+1]&&St[a+1]||St[a],y=i&&("complete"===l||"reset"===l||l in i))),qe&&(m||y)&&(y||Je||!i)&&(S(qe)?qe(c):c.getTrailing(qe).forEach(function(e){return e.endAnimation()})),Q||(!E||p||ve?i&&i.totalProgress(s,!!p):((_||$&&$!==c)&&E.render(E._dp._time-E._start),E.resetTo?E.resetTo("totalProgress",s,i._tTime/i._tDur):(E.vars.totalProgress=s,E.invalidate().restart()))),d)&&(e&&J&&(X.style[J+g.os2]=Mt),de?b&&((O=!e&&u<s&&o<W+1&&o+1>=P(w,g),pt)&&(e||!f&&!O?Yt(d,X):(F=D(d,!0),T=o-x,Yt(d,r,F.top+(g===h?T:0)+v,F.left+(g===h?0:T)+v))),re(f||O?tt:rt),pe!==M&&s<1&&f||ct(Pe+(1!==s||O?0:pe))):ct(ie(Pe+pe*s))),!k||V.tween||p||ve||ye.restart(!0),Ee&&(m||mt&&s&&(s<1||!et))&&Ne(Ee.targets).forEach(function(e){return e.classList[f||mt?"add":"remove"](Ee.className)}),!Me||Q||e||Me(c),b&&!p?(Q&&(y&&("complete"===l?i.pause().totalProgress(1):"reset"===l?i.restart(!0).pause():"restart"===l?i.restart(!0):i[l]()),Me&&Me(c)),!m&&et||(zt&&m&&lt(c,zt),We[a]&&lt(c,We[a]),mt&&(1===s?c.kill(!1,1):We[a]=0),m||We[a=1===s?1:3]&&lt(c,We[a])),jt&&!f&&Math.abs(c.getVelocity())>(ae(jt)?jt:2500)&&(ge(c.callbackAnimation),E?E.progress(1):ge(i,!s,1))):Q&&Me&&!p&&Me(c)),yt&&(z=_?o/_.duration()*(_._caScrollDist||0):o,Dt(z+(ce._isFlipped?1:0)),yt(z)),_t&&_t(-o/_.duration()*(_._caScrollDist||0))}},c.enable=function(e,t){c.enabled||(c.enabled=!0,f(w,"resize",be),f(oe?a:w,"scroll",ne),Ae&&f(l,"refreshInit",Ae),!1!==e&&(c.progress=Fe=0,xe=Le=Be=N()),!1!==t&&c.refresh())},c.getTween=function(e){return e&&V?V.tween:E},c.setPositions=function(e,t){d&&(Pe+=e-x,pe+=t-e-M),c.start=x=e,c.end=W=t,M=t-e,c.update()},c.disable=function(e,t){if(c.enabled&&(!1!==e&&c.revert(),c.enabled=c.isActive=!1,t||E&&E.pause(),De=0,U&&(U.uncache=1),Ae&&m(l,"refreshInit",Ae),ye&&(ye.pause(),V.tween&&V.tween.kill()&&(V.tween=0)),!oe)){for(var n=s.length;n--;)if(s[n].scroller===w&&s[n]!==c)return;m(w,"resize",be),m(w,"scroll",ne)}},c.kill=function(e,t){c.disable(e,t),E&&!t&&E.kill(),K&&delete st[K];var n=s.indexOf(c);0<=n&&s.splice(n,1),n===y&&0<ke&&y--,n=0,s.forEach(function(e){return e.scroller===c.scroller&&(n=1)}),n||(c.scroll.rec=0),i&&(i.scrollTrigger=null,e&&i.render(-1),t||i.kill()),le&&[le,Te,ce,we].forEach(function(e){return e.parentNode&&e.parentNode.removeChild(e)}),$===c&&($=0),d&&(U&&(U.uncache=1),n=0,s.forEach(function(e){return e.pin===d&&n++}),n||(U.spacer=0)),o.onKill&&o.onKill(c)},c.enable(!1,!1),He&&He(c),i&&i.add&&!M?t.delayedCall(.01,function(){return x||W||c.refresh()})&&(M=.01)&&(x=W=0):c.refresh()}else this.update=this.refresh=this.kill=R},l.register=function(n){return U||(t=n||Dt(),St()&&window.document&&l.enable(),U=Ye),U},l.defaults=function(t){if(t)for(var n in t)Ve[n]=t[n];return Ve},l.disable=function(t,o){Ye=0,s.forEach(function(e){return e[o?"kill":"disable"](t)}),m(n,"wheel",ne),m(a,"scroll",ne),clearInterval(Pe),m(a,"touchcancel",R),m(r,"touchstart",R),Le(m,a,"pointerdown,touchstart,mousedown",Ht),Le(m,a,"pointerup,touchend,mouseup",vt),De.kill(),Fe(m);for(var c=0;c<i.length;c+=3)We(m,i[c],i[c+1]),We(m,i[c],i[c+2])},l.enable=function(){if(n=window,a=document,L=a.documentElement,r=a.body,t&&(Ne=t.utils.toArray,ee=t.utils.clamp,dt=t.core.suppressOverwrites||R,t.core.globals("ScrollTrigger",l),r)){Ye=1,d.register(t),l.isTouch=d.isTouch,B=d.isTouch&&/(iPad|iPhone|iPod|Mac)/g.test(navigator.userAgent),f(n,"wheel",ne),Gt=[n,a,L,r],l.matchMedia({"(orientation: portrait)":function(){return _t(),_t}}),f(a,"scroll",ne);var o,u,c=r.style,p=c.borderTopStyle;for(c.borderTopStyle="solid",u=D(r),h.m=Math.round(u.top+h.sc())||0,b.m=Math.round(u.left+b.sc())||0,p?c.borderTopStyle=p:c.removeProperty("border-top-style"),Pe=setInterval(jt,250),t.delayedCall(.5,function(){return ve=0}),f(a,"touchcancel",R),f(r,"touchstart",R),Le(f,a,"pointerdown,touchstart,mousedown",Ht),Le(f,a,"pointerup,touchend,mouseup",vt),ot=t.utils.checkPrefix("transform"),Ee.push(ot),U=j(),De=t.delayedCall(.2,J).pause(),Q=[a,"visibilitychange",function(){var e=n.innerWidth,t=n.innerHeight;a.hidden?(wt=e,bt=t):wt===e&&bt===t||be()},a,"DOMContentLoaded",J,n,"load",J,n,"resize",be],Fe(f),s.forEach(function(e){return e.enable(0,1)}),o=0;o<i.length;o+=3)We(m,i[o],i[o+1]),We(m,i[o],i[o+2])}},l.config=function(t){"limitCallbacks"in t&&(et=!!t.limitCallbacks);var n=t.syncInterval;n&&clearInterval(Pe)||(Pe=n)&&setInterval(jt,n),"ignoreMobileResize"in t&&(Wt=1===l.isTouch&&t.ignoreMobileResize),"autoRefreshEvents"in t&&(Fe(m)||Fe(f,t.autoRefreshEvents||"none"),pt=-1===(t.autoRefreshEvents+"").indexOf("resize"))},l.scrollerProxy=function(t,s){var o=O(t),a=i.indexOf(o),c=G(o);~a&&i.splice(a,c?6:2),s&&(c?F.unshift(n,s,r,s,L,s):F.unshift(o,s))},l.matchMedia=function(t){var s,o,i,a,r;for(i in t)s=c.indexOf(i),a=t[i],"all"===(Z=i)?a():(o=n.matchMedia(i))&&(o.matches&&(r=a()),~s?(c[s+1]=Vt(c[s+1],a),c[s+2]=Vt(c[s+2],r)):(s=c.length,c.push(i,a,r),o.addListener?o.addListener(At):o.addEventListener("change",At)),c[s+3]=o.matches),Z=0;return c},l.clearMatchMedia=function(t){t||(c.length=0),0<=(t=c.indexOf(t))&&c.splice(t,4)},l.isInViewport=function(t,s,o){var i=(T(t)?O(t):t).getBoundingClientRect(),a=i[o?Y:q]*s||0;return o?0<i.right-a&&i.left+a<n.innerWidth:0<i.bottom-a&&i.top+a<n.innerHeight},l.positionInViewport=function(t,s,o){T(t)&&(t=O(t));var i=t.getBoundingClientRect(),a=i[o?Y:q],r=s==null?a/2:s in Be?Be[s]*a:~s.indexOf("%")?parseFloat(s)*a/100:parseFloat(s)||0;return o?(i.left+r)/n.innerWidth:(i.top+r)/n.innerHeight},l);function l(e,n){U||l.register(t)||console.warn("Please gsap.registerPlugin(ScrollTrigger)"),this.init(e,n)}o.version="3.10.5",o.saveStyles=function(e){return e?Ne(e).forEach(function(e){if(e&&e.style){var n=E.indexOf(e);0<=n&&E.splice(n,5),E.push(e,e.style.cssText,e.getBBox&&e.getAttribute("transform"),t.core.getCache(e),Z)}}):E},o.revert=function(e,t){return Te(!e,t)},o.create=function(e,t){return new o(e,t)},o.refresh=function(e){return e?be():(U||o.register())&&J(!0)},o.update=ce,o.clearScrollMemory=kt,o.maxScroll=function(e,t){return P(e,t?b:h)},o.getScrollFunc=function(e,t){return I(O(e),t?b:h)},o.getById=function(e){return st[e]},o.getAll=function(){return s.filter(function(e){return"ScrollSmoother"!==e.vars.id})},o.isScrolling=function(){return!!C},o.snapDirectional=Ze,o.addEventListener=function(e,t){var n=de[e]||(de[e]=[]);~n.indexOf(t)||n.push(t)},o.removeEventListener=function(e,t){var n=de[e],s=n&&n.indexOf(t);0<=s&&n.splice(s,1)},o.batch=function(e,n){function c(e,n){var s=[],o=[],a=t.delayedCall(l,function(){n(s,o),s=[],o=[]}).pause();return function(e){s.length||a.restart(!0),s.push(e.trigger),o.push(e),i<=s.length&&a.progress(1)}}var s,r=[],a={},l=n.interval||.016,i=n.batchMax||1e9;for(s in n)a[s]="on"===s.substr(0,2)&&S(n[s])&&"onRefreshInit"!==s?c(0,n[s]):n[s];return S(i)&&(i=i(),f(o,"refresh",function(){return i=n.batchMax()})),Ne(e).forEach(function(e){var t={};for(s in a)t[s]=a[s];t.trigger=e,r.push(o.create(t))}),r};function Zt(e,t,n,s){return s<t?e(s):t<0&&e(0),s<n?(s-t)/(n-t):n<0?t/(t-n):1}function we(e,t){!0===t?e.style.removeProperty("touch-action"):e.style.touchAction=!0===t?"auto":t?"pan-"+t+(d.isTouch?" pinch-zoom":""):"none",e===L&&we(r,t)}function en(e){var i,o=e.event,r=e.target,c=e.axis,n=(o.changedTouches?o.changedTouches[0]:o).target,s=n._gsap||t.core.getCache(n),a=j();if(!s._isScrollT||2e3<a-s._isScrollT){for(;n&&n.scrollHeight<=n.clientHeight;)n=n.parentNode;s._isScroll=n&&!G(n)&&n!==r&&(zt[(i=z(n)).overflowY]||zt[i.overflowX]),s._isScrollT=a}!s._isScroll&&"x"!==c||(o._gsapAllow=!0)}function ft(e,t,n,s){return d.create({target:e,capture:!0,debounce:!1,lockAxis:!0,type:t,onWheel:s=s&&en,onPress:s,onDrag:s,onScroll:s,onEnable:function(){return n&&f(a,d.eventTypes[0],Ot,!1,!0)},onDisable:function(){return m(a,d.eventTypes[0],Ot,!0)}})}function nn(e){function H(){return _=!1}function C(){c=P(r,h),v=ee(B?1:0,c),u&&(M=ee(0,P(r,b))),y=Me}function F(){a._gsap.y=ie(parseFloat(a._gsap.y)+s.offset)+"px",a.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+parseFloat(a._gsap.y)+", 0, 1)",s.offset=s.cacheID=0}function w(){C(),o.isActive()&&o.vars.scrollY>c&&(s()>c?o.progress(1)&&s(c):o.resetTo("scrollY",c))}Se(e)||(e={}),e.preventDefault=e.isNormalizer=e.allowClicks=!0,e.type||(e.type="wheel,touch"),e.debounce=!!e.debounce,e.id=e.id||"normalizer";var o,c,l,y,_,E,k,N,u=e.normalizeScrollX,x=e.momentum,W=e.allowNestedScroll,r=O(e.target)||L,T=t.core.globals().ScrollSmoother,A=T&&T.get(),a=B&&(e.content&&O(e.content)||A&&!1!==e.content&&!A.smooth()&&A.content()),s=I(r,h),g=I(r,b),p=1,V=(d.isTouch&&n.visualViewport?n.visualViewport.scale*n.visualViewport.width:n.outerWidth)/n.innerWidth,z=0,$=S(x)?function(){return x(l)}:function(){return x||2.8},D=ft(r,e.type,!0,W),M=R,v=R;return a&&t.set(a,{y:"+=0"}),e.ignoreCheck=function(e){return B&&"touchmove"===e.type&&function(){if(_){requestAnimationFrame(H);var t,o=ie(l.deltaY/2),n=v(s.v-o);return a&&n!==s.v+s.offset&&(s.offset=n-s.v,t=ie((parseFloat(a&&a._gsap.y)||0)-s.offset),a.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+t+", 0, 1)",a._gsap.y=t+"px",s.cacheID=i.cache,ce()),!0}s.offset&&F(),_=!0}()||1.05<p&&"touchstart"!==e.type||l.isGesturing||e.touches&&1<e.touches.length},e.onPress=function(){var e=p;p=ie((n.visualViewport&&n.visualViewport.scale||1)/V),o.pause(),e!==p&&we(r,1.01<p||!u&&"x"),N=g(),E=s(),C(),y=Me},e.onRelease=e.onGestureStart=function(e,n){if(s.offset&&F(),n){i.cache++;var a,d,l=$();u&&(a=(d=g())+.05*l*-e.velocityX/.227,l*=Zt(g,d,a,P(r,b)),o.vars.scrollX=M(a)),a=(d=s())+.05*l*-e.velocityY/.227,l*=Zt(s,d,a,P(r,h)),o.vars.scrollY=v(a),o.invalidate().duration(l).play(.01),(B&&o.vars.scrollY>=c||c-1<=d)&&t.to({},{onUpdate:w,duration:l})}else k.restart(!0)},e.onWheel=function(){o._ts&&o.pause(),1e3<j()-z&&(y=0,z=j())},e.onChange=function(e,t,n,o,i){if(Me!==y&&C(),t&&u&&g(M(o[2]===t?N+(e.startX-e.x):g()+t-o[1])),n){s.offset&&F();var c=i[2]===n,a=c?E+e.startY-e.y:s()+n-i[1],r=v(a);c&&a!==r&&(E+=r-a),s(r)}(n||t)&&ce()},e.onEnable=function(){we(r,!u&&"x"),f(n,"resize",w),D.enable()},e.onDisable=function(){we(r,!0),m(n,"resize",w),D.kill()},e.lockAxis=!1!==e.lockAxis,((l=new d(e)).iOS=B)&&!s()&&s(1),B&&t.ticker.add(R),k=l._dc,o=t.to(l,{ease:"power4",paused:!0,scrollX:u?"+=0.1":"+=0",scrollY:"+=0.1",onComplete:k.vars.onComplete}),l}var Kt,zt={auto:1,scroll:1},an=/(input|label|select|textarea)/i,Ot=function(t){var n=an.test(t.target.tagName);(n||Kt)&&(t._gsapAllow=!0,Kt=n)};o.sort=function(e){return s.sort(e||function(e,t){return-1e6*(e.vars.refreshPriority||0)+e.start-(t.start+-1e6*(t.vars.refreshPriority||0))})},o.observe=function(e){return new d(e)},o.normalizeScroll=function(e){if(void 0===e)return k;if(!0===e&&k)return k.enable();if(!1===e)return k&&k.kill();var t=e instanceof d?e:nn(e);return k&&k.target===t.target&&k.kill(),G(t.target)&&(k=t),t},o.core={_getVelocityProp:Ge,_inputObserver:ft,_scrollers:i,_proxies:F,bridge:{ss:function(){C||W("scrollStart"),C=j()},ref:function(){return p}}},Dt()&&t.registerPlugin(o),e.ScrollTrigger=o,e.default=o,typeof window=="undefined"||window!==e?Object.defineProperty(e,"__esModule",{value:!0}):delete e.default})